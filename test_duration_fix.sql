-- 测试 duration_minutes 和 subject_scores/module_scores 修复是否成功
-- 这个脚本测试视图是否能正常工作，使用V53表的实际数据构造JSON字段

-- 1. 测试视图是否能正常查询
SELECT
    '=== 测试 v_sat_exam_records 视图 ===' as test_info;

-- 2. 查询视图的前几条记录，验证字段修复
SELECT
    user_id,
    paper_id,
    paper_name,
    exam_type,
    latest_session_id,
    latest_total_score,
    latest_reading_score,
    latest_math_score,
    latest_duration_minutes,
    latest_exam_status,
    is_improving
FROM v_sat_exam_records
LIMIT 5;

-- 3. 测试视图的字段定义
SELECT
    '=== 视图字段信息 ===' as test_info;

SELECT
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'v_sat_exam_records'
    AND (column_name LIKE '%duration%' OR column_name LIKE '%scores%')
ORDER BY ordinal_position;

-- 4. 测试表结构
SELECT
    '=== t_sat_mock_exam_history 表字段信息 ===' as test_info;

SELECT
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 't_sat_mock_exam_history'
    AND (column_name LIKE '%time%' OR column_name LIKE '%score%')
ORDER BY ordinal_position;

-- 5. 验证视图创建是否成功
SELECT
    '=== 验证视图是否存在 ===' as test_info;

SELECT
    schemaname,
    viewname,
    viewowner
FROM pg_views
WHERE viewname = 'v_sat_exam_records';
