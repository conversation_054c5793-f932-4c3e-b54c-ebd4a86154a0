-- V54: 创建考试记录功能相关表和视图
--
-- 为SAT考试系统添加考试记录统计功能，支持用户查看考试历史、成绩趋势等
-- 注意：t_sat_mock_exam_history 表已经包含了所有需要的字段，无需修改

-- 1. 创建考试统计汇总表
-- 该表用于存储每个用户每个试卷的聚合统计信息，提高查询性能
CREATE TABLE IF NOT EXISTS "t_sat_exam_statistics" (
    "id" BIGSERIAL PRIMARY KEY,
    "user_id" BIGINT NOT NULL,
    "paper_id" BIGINT NOT NULL,
    "paper_name" VARCHAR(255) NOT NULL,
    "exam_type" VARCHAR(20) NOT NULL CHECK ("exam_type" IN ('full', 'math', 'reading')),
    
    -- 考试次数统计
    "exam_count" INTEGER NOT NULL DEFAULT 0,
    "completed_count" INTEGER NOT NULL DEFAULT 0,
    "partial_count" INTEGER NOT NULL DEFAULT 0,
    
    -- 分数统计
    "best_score" INTEGER,
    "latest_score" INTEGER,
    "average_score" DECIMAL(6,2),
    "score_trend" VARCHAR(20) CHECK ("score_trend" IN ('improving', 'declining', 'stable')),
    
    -- 最近考试信息
    "latest_session_id" VARCHAR(255),
    "latest_exam_at" TIMESTAMPTZ,
    
    -- 进步趋势
    "score_change" INTEGER DEFAULT 0,  -- 与上次相比的分数变化
    "trend_description" VARCHAR(100),
    
    -- 时间统计
    "total_study_time_minutes" INTEGER DEFAULT 0,
    "average_duration_minutes" INTEGER,
    
    -- 元数据
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一约束：每个用户每个试卷每种考试类型只有一条统计记录
    UNIQUE("user_id", "paper_id", "exam_type")
);

-- 2. 创建索引以优化查询性能
-- 为考试历史表创建必要的索引（如果不存在的话）
CREATE INDEX IF NOT EXISTS "idx_exam_history_user_paper" 
ON "t_sat_mock_exam_history"("user_id", "paper_id");

CREATE INDEX IF NOT EXISTS "idx_exam_history_user_completed" 
ON "t_sat_mock_exam_history"("user_id", "completed_at" DESC) 
WHERE "exam_status" = 'completed';

CREATE INDEX IF NOT EXISTS "idx_exam_history_paper_type" 
ON "t_sat_mock_exam_history"("paper_id", "exam_type");

CREATE INDEX IF NOT EXISTS "idx_exam_history_session_id" 
ON "t_sat_mock_exam_history"("session_id");

-- 为统计表创建索引
CREATE INDEX IF NOT EXISTS "idx_exam_statistics_user_id" 
ON "t_sat_exam_statistics"("user_id");

CREATE INDEX IF NOT EXISTS "idx_exam_statistics_paper_id" 
ON "t_sat_exam_statistics"("paper_id");

CREATE INDEX IF NOT EXISTS "idx_exam_statistics_user_latest" 
ON "t_sat_exam_statistics"("user_id", "latest_exam_at" DESC);

CREATE INDEX IF NOT EXISTS "idx_exam_statistics_paper_performance" 
ON "t_sat_exam_statistics"("paper_id", "average_score" DESC);

-- 3. 创建考试记录视图，用于API查询
CREATE OR REPLACE VIEW "v_sat_exam_records" AS
SELECT
    s.user_id,
    s.paper_id,
    s.paper_name,
    s.exam_type,
    s.exam_count,
    s.completed_count,
    s.best_score,
    s.latest_score,
    s.average_score,
    s.score_change,
    s.trend_description,

    -- 最近考试详情（使用现有字段）
    h.session_id as latest_session_id,
    h.total_score as latest_total_score,
    h.reading_score as latest_reading_score,
    h.math_score as latest_math_score,
    h.accuracy_rate as latest_accuracy_rate,
    h.completed_at as latest_completed_at,
    CASE
        WHEN h.total_time_seconds IS NOT NULL AND h.total_time_seconds > 0
        THEN ROUND(h.total_time_seconds / 60.0)::INTEGER
        ELSE NULL
    END as latest_duration_minutes,
    h.completion_percentage as latest_exam_progress,  -- 使用现有的 completion_percentage 字段
    h.exam_status as latest_exam_status,  -- 使用现有的 exam_status 字段

    -- 进步趋势
    CASE
        WHEN s.score_change > 0 THEN true
        ELSE false
    END as is_improving

FROM "t_sat_exam_statistics" s
LEFT JOIN "t_sat_mock_exam_history" h ON h.session_id = s.latest_session_id;


-- 5. 添加注释
COMMENT ON TABLE "t_sat_exam_statistics" IS '考试统计汇总表，存储每个用户每个试卷的考试统计信息';
COMMENT ON VIEW "v_sat_exam_records" IS '考试记录视图，用于API查询，包含统计信息和最近考试详情';

