#!/usr/bin/env python3
"""
测试考试类型逻辑的脚本
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_create_session_scenarios():
    """测试不同的创建会话场景"""
    
    # 场景1：新用户创建full类型会话
    print("=== 场景1：新用户创建full类型会话 ===")
    test_create_session(54321, "full")
    
    # 场景2：同一用户再次创建full类型会话（应该返回已存在会话）
    print("\n=== 场景2：同一用户再次创建full类型会话 ===")
    test_create_session(54321, "full")
    
    # 场景3：同一用户尝试创建math类型会话（应该返回错误）
    print("\n=== 场景3：同一用户尝试创建math类型会话 ===")
    test_create_session(54321, "math")
    
    # 场景4：新用户创建math类型会话
    print("\n=== 场景4：新用户创建math类型会话 ===")
    test_create_session(65432, "math")
    
    # 场景5：新用户创建reading类型会话
    print("\n=== 场景5：新用户创建reading类型会话 ===")
    test_create_session(76543, "reading")

def test_create_session(user_id, exam_type):
    """测试创建考试会话"""
    url = f"{BASE_URL}/exam/session/create"
    
    payload = {
        "user_id": user_id,
        "paper_id": 700166615844655104,
        "exam_type": exam_type
    }
    
    print(f"请求: 用户ID={user_id}, 考试类型={exam_type}")
    
    try:
        response = requests.post(url, json=payload)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                session_data = data.get("data", {})
                print(f"✅ 成功: session_id={session_data.get('session_id')}")
                print(f"   exam_type={session_data.get('exam_type')}")
                print(f"   total_questions={session_data.get('total_questions')}")
                
                # 检查sections
                sections = session_data.get("sections", [])
                print(f"   sections数量: {len(sections)}")
                for section in sections:
                    print(f"   - {section['section_name']}: {len(section['modules'])}个模块")
                    
                    # 检查自适应模块信息
                    for i, module in enumerate(section['modules']):
                        print(f"     模块{i+1}: {module['module_type']} ({module['status']})")
                        if module.get('adaptive_info'):
                            adaptive = module['adaptive_info']
                            print(f"       自适应: 依赖={adaptive['depends_on']}, 可能类型={adaptive['possible_types']}")
                            if adaptive.get('actual_type'):
                                print(f"       实际类型: {adaptive['actual_type']}")
            else:
                print(f"❌ 业务错误: {data.get('message')}")
        else:
            print(f"❌ HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_create_session_scenarios()
