# 考试记录试卷接口变更说明

## 变更概述

将 `/api/v1/exam/records/paper` 接口从 GET 方法改为 POST 方法，并将参数从查询参数改为请求体。

## 变更详情

### 变更前 (GET 方法)

**请求方式**: GET  
**URL**: `/api/v1/exam/records/paper?user_id=1&paper_ids=700540150387707904,2,3`

**查询参数**:
- `user_id`: 用户ID (整数)
- `paper_ids`: 试卷ID列表，逗号分隔的字符串

**示例请求**:
```bash
curl -X GET "http://localhost:8000/api/v1/exam/records/paper?user_id=1&paper_ids=700540150387707904,2,3"
```

### 变更后 (POST 方法)

**请求方式**: POST  
**URL**: `/api/v1/exam/records/paper`  
**Content-Type**: `application/json`

**请求体**:
```json
{
  "user_id": 1,
  "paper_ids": [700540150387707904, 2, 3]
}
```

**示例请求**:
```bash
curl -X POST "http://localhost:8000/api/v1/exam/records/paper" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1,
    "paper_ids": [700540150387707904, 2, 3]
  }'
```

## 变更原因

1. **数据类型一致性**: `paper_ids` 现在是整数数组而不是逗号分隔的字符串
2. **更好的数据验证**: 可以在反序列化阶段验证数据类型
3. **避免URL长度限制**: 大量试卷ID不会导致URL过长
4. **更符合RESTful规范**: 复杂的查询条件使用POST请求体更合适

## 响应格式

响应格式保持不变：

```json
{
  "code": 0,
  "message": "SUCCESS",
  "data": {
    "paper_statistics": [
      {
        "user_id": 1,
        "paper_id": 700540150387707904,
        "paper_name": "SAT Practice Test 1",
        "exam_type": "Full",
        "exam_count": 3,
        "completed_count": 2,
        "best_score": 1450,
        "latest_score": 1420,
        "average_score": 1400,
        "last_exam_detail": {
          "session_id": "session_123",
          "total_score": 1420,
          "reading_score": 720,
          "math_score": 700,
          "accuracy_rate": 0.85,
          "completed_at": "2024-01-15T10:30:00Z",
          "duration_minutes": 180,
          "exam_progress": 100,
          "exam_status": "Completed"
        },
        "progress_trend": {
          "is_improving": true,
          "score_change": 20,
          "trend_description": "进步中"
        }
      }
    ]
  }
}
```

## 错误处理

### 空试卷ID列表

**请求**:
```json
{
  "user_id": 1,
  "paper_ids": []
}
```

**响应**:
```json
{
  "code": 1001,
  "message": "试卷ID列表不能为空",
  "data": {}
}
```

### 缺少必需字段

**请求**:
```json
{
  "user_id": 1
}
```

**响应**: HTTP 400 Bad Request

## 迁移指南

如果你正在使用旧的GET接口，请按以下步骤迁移：

1. 将HTTP方法从GET改为POST
2. 添加`Content-Type: application/json`请求头
3. 将查询参数转换为JSON请求体
4. 将`paper_ids`从逗号分隔字符串改为整数数组

### 迁移示例

**旧代码** (JavaScript):
```javascript
const response = await fetch(
  `/api/v1/exam/records/paper?user_id=${userId}&paper_ids=${paperIds.join(',')}`
);
```

**新代码** (JavaScript):
```javascript
const response = await fetch('/api/v1/exam/records/paper', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    user_id: userId,
    paper_ids: paperIds
  })
});
```

## 测试

可以使用以下测试用例验证接口功能：

```bash
# 正常请求
curl -X POST "http://localhost:8000/api/v1/exam/records/paper" \
  -H "Content-Type: application/json" \
  -d '{"user_id": 1, "paper_ids": [700540150387707904, 2, 3]}'

# 空试卷ID列表（应返回错误）
curl -X POST "http://localhost:8000/api/v1/exam/records/paper" \
  -H "Content-Type: application/json" \
  -d '{"user_id": 1, "paper_ids": []}'

# 缺少字段（应返回错误）
curl -X POST "http://localhost:8000/api/v1/exam/records/paper" \
  -H "Content-Type: application/json" \
  -d '{"user_id": 1}'
```
