# SAT考试题目获取API文档

## 概述

本文档描述了SAT考试系统中题目获取的API接口。系统采用标准的考试流程：创建会话 → 开始模块 → 获取题目内容 → 提交答案。

## 数据流程

```
1. 创建考试会话 → POST /api/v1/exam/session/create
2. 开始模块 → POST /api/v1/exam/module/start (返回完整题目内容)
3. 提交答案 → POST /api/v1/exam/answer/submit
```

## API接口

### 1. 开始模块 (获取模块题目)

**接口**: `POST /api/v1/exam/module/start`

**功能**: 开始指定模块，返回该模块的完整题目内容（包含题目内容、选项等详细信息）

**请求参数**:
```json
{
    "session_id": "string",     // 会话ID
    "module_type": "1|2E|2H",   // 模块类型
    "user_id": 123              // 用户ID
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "session_id": "session_123",
        "module_info": {
            "module_type": "1",
            "question_count": 22,
            "time_limit_minutes": 35,
            "difficulty_level": "standard"
        },
        "questions": [
            {
                "id": 1001,
                "subject_id": 1,
                "knowledge_id": 101,
                "type_id": 1,
                "content": {                // 完整的题目内容
                    "question_text": "题目文本...",
                    "images": ["image1.jpg"]
                },
                "options": {                // 完整的选项内容
                    "A": "选项A内容",
                    "B": "选项B内容",
                    "C": "选项C内容",
                    "D": "选项D内容"
                },
                "answer": null,             // 考试时不返回答案
                "explanation": null,        // 考试时不返回解析
                "elo_rating": 1500.0,
                "module_sequence": 1
            }
        ],
        "answered_questions": [],           // 已答题目索引
        "remaining_time_seconds": 2100,     // 剩余时间
        "current_question_index": 1         // 当前题目索引
    }
}
```

### 2. 提交答案

**接口**: `POST /api/v1/exam/answer/submit`

**功能**: 提交学生对某道题的答案

**请求参数**:
```json
{
    "session_id": "string",     // 会话ID
    "user_id": 123,             // 用户ID
    "question_id": 1001,        // 题目ID
    "student_answer": "A",      // 学生答案
    "time_spent_seconds": 120,  // 答题用时
    "module_type": "1",         // 模块类型
    "question_sequence": 1      // 题目序号
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": true,
        "is_correct": true,
        "correct_answer": "A",
        "explanation": "解析内容...",
        "module_progress": {
            "answered_count": 5,
            "total_count": 22,
            "correct_count": 4,
            "remaining_time_seconds": 1800
        }
    }
}
```

## 业务逻辑

### 试卷版本选择
- 根据`paper_id`查找`version`最高且`status=5`的版本
- 确保获取的是最新发布版本的试卷

### 题目内容返回策略
- **开始模块时**: 返回完整的题目内容和选项，但不返回`answer`和`explanation`字段
- **提交答案后**: 在答案提交响应中返回正确答案和解析

### 模块类型说明
- `1`: 第一模块（标准难度）
- `2E`: 第二模块简单版（根据第一模块表现决定）
- `2H`: 第二模块困难版（根据第一模块表现决定）

### 学科分类
- `subject_id = 1`: 数学
- `subject_id = 14`: 语言

## 数据库表结构

### t_sat_paper (试卷表)
```sql
CREATE TABLE t_sat_paper (
    paper_id BIGINT PRIMARY KEY,
    papar_name VARCHAR(255),        -- 试卷名称
    paper_inner_name VARCHAR(255),  -- 内部名称
    version INTEGER,                -- 版本号
    status INTEGER,                 -- 状态(5=发布)
    subject_id INTEGER              -- 学科ID
);
```

### t_sat_paper_question (试卷题目关联表)
```sql
CREATE TABLE t_sat_paper_question (
    paper_id BIGINT,
    question_id INTEGER,
    module_type VARCHAR(10),        -- 模块类型
    module_sequence INTEGER,        -- 模块内顺序
    subject_id INTEGER,             -- 学科ID
    subject_group_id INTEGER,       -- 学科组ID
    PRIMARY KEY (paper_id, question_id)
);
```

## 测试数据

系统已预置测试数据：
- **试卷ID**: `700540150387707904`
- **版本**: 1, 2 (status=5)
- **包含题目**: 数学和语言各模块的题目

## 错误处理

常见错误码：
- `SESSION_NOT_FOUND`: 会话不存在
- `PERMISSION_DENIED`: 用户无权访问
- `PAPER_NOT_FOUND`: 试卷不存在
- `MODULE_ALREADY_STARTED`: 模块已开始
- `GET_QUESTIONS_ERROR`: 获取题目失败

## 完整API流程示例

### 1. 创建考试会话
```bash
curl -X POST http://localhost:8000/api/v1/exam/session/create \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12345,
    "paper_id": 700540150387707904,
    "exam_type": "full"
  }'
```

### 2. 开始模块
```bash
curl -X POST http://localhost:8000/api/v1/exam/module/start \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "session_123",
    "user_id": 12345,
    "module_type": "1"
  }'
```

### 3. 提交答案
```bash
curl -X POST http://localhost:8000/api/v1/exam/answer/submit \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "session_123",
    "user_id": 12345,
    "question_id": 1001,
    "student_answer": "A",
    "time_spent_seconds": 120,
    "module_type": "1",
    "question_sequence": 1
  }'
```

### 4. 提交考试
```bash
curl -X POST http://localhost:8000/api/v1/exam/submit \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "session_123",
    "user_id": 12345,
    "force_submit": true
  }'
```

## 测试脚本

使用提供的Python测试脚本：
```bash
python3 scripts/test_exam_api.py
```

## 性能优化

1. **数据库查询优化**:
   - 使用联合查询一次性获取模块的所有题目详细内容
   - 创建适当的索引提升查询性能
   - 按paper_id查找最高版本且status=5的试卷

2. **内存优化**:
   - 使用HashMap缓存题目内容映射
   - 一次性加载模块所有题目，避免多次查询

3. **网络优化**:
   - 开始模块时一次性返回所有题目内容，减少网络请求次数
   - 考试时过滤敏感信息（答案、解析）减少传输量

4. **成绩计算优化**:
   - 实现真实的SAT分数计算算法
   - 支持按学科分别计算成绩
