# SAT考试模块提交API文档

## 概述

本文档描述了SAT考试系统中模块提交相关的API接口。这些接口支持SAT考试的完整流程，包括模块提交、成绩计算、自适应逻辑等功能。

## 接口列表

### 1. 提交模块

**接口地址**: `POST /api/v1/exam/module/submit`

**功能描述**: 提交考试模块，计算模块成绩，触发自适应逻辑（如果是第一模块）

**请求参数**:
```json
{
    "session_id": "mock_exam_20241128_A1B2C3D4",
    "user_id": 12345,
    "module_type": "1",
    "force_submit": false
}
```

**参数说明**:
- `session_id`: 会话ID（必需）
- `user_id`: 用户ID（必需）
- `module_type`: 模块类型，"1"表示第一模块，"2E"表示第二模块简单版，"2H"表示第二模块困难版（必需）
- `force_submit`: 是否强制提交，即使未完成所有题目（可选，默认false）

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "success": true,
        "module_score": {
            "module_type": "1",
            "subject": "math",
            "subject_name": "数学",
            "raw_score": 18,
            "max_score": 22,
            "accuracy_rate": 0.818,
            "sat_score": 690,
            "score_range": {
                "min_score": 660,
                "max_score": 720
            }
        },
        "module_statistics": {
            "total_questions": 22,
            "answered_questions": 22,
            "correct_questions": 18,
            "skipped_questions": 0,
            "average_time_per_question": 95.5,
            "total_time_seconds": 2101,
            "remaining_time_seconds": null,
            "completion_rate": 1.0,
            "accuracy_rate": 0.818
        },
        "next_action": {
            "action_type": "start_module2",
            "description": "第一模块已完成，可以开始第二模块",
            "can_continue": true,
            "suggested_wait_seconds": null
        },
        "adaptive_info": {
            "module1_performance_score": 81.8,
            "module2_type": "2H",
            "adaptive_reason": "第一模块正确率81.8%达到阈值65.0%，进入困难模块",
            "threshold_info": {
                "threshold": 65.0,
                "actual_score": 81.8,
                "threshold_met": true
            }
        }
    },
    "timestamp": 1701234567
}
```

**响应字段说明**:
- `success`: 提交是否成功
- `module_score`: 模块成绩信息
  - `module_type`: 模块类型
  - `subject`: 学科（math/reading）
  - `subject_name`: 学科名称
  - `raw_score`: 原始分数（答对题数）
  - `max_score`: 满分
  - `accuracy_rate`: 正确率（0-1）
  - `sat_score`: SAT分数（200-800）
  - `score_range`: 分数区间
- `module_statistics`: 模块统计信息
  - `total_questions`: 总题数
  - `answered_questions`: 已答题数
  - `correct_questions`: 答对题数
  - `skipped_questions`: 跳过题数
  - `average_time_per_question`: 平均答题时间（秒，仅统计用）
  - `total_time_seconds`: 总用时（秒，仅统计用）
  - `remaining_time_seconds`: 剩余时间（null，时间管理已关闭）
  - `completion_rate`: 完成率（0-1）
  - `accuracy_rate`: 正确率（0-1）
- `next_action`: 下一步操作建议
  - `action_type`: 操作类型（start_module2/switch_subject/take_break/complete_exam）
  - `description`: 操作描述
  - `can_continue`: 是否可以继续
  - `suggested_wait_seconds`: 建议等待时间（休息时间）
- `adaptive_info`: 自适应信息（仅第一模块有此字段）
  - `module1_performance_score`: 第一模块表现分数
  - `module2_type`: 确定的第二模块类型
  - `adaptive_reason`: 自适应原因说明
  - `threshold_info`: 阈值信息

### 2. 获取模块成绩

**接口地址**: `GET /api/v1/exam/module/score`

**功能描述**: 获取指定模块的成绩信息

**请求参数**:
```
GET /api/v1/exam/module/score?session_id=mock_exam_20241128_A1B2C3D4&user_id=12345&module_type=1
```

**参数说明**:
- `session_id`: 会话ID（必需）
- `user_id`: 用户ID（必需）
- `module_type`: 模块类型（必需）

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "mock_exam_20241128_A1B2C3D4",
        "module_type": "1",
        "module_status": "submitted",
        "score_info": {
            "raw_score": 18,
            "max_score": 22,
            "accuracy_rate": 0.818,
            "sat_score": 690,
            "score_range": {
                "min_score": 660,
                "max_score": 720
            }
        }
    },
    "timestamp": 1701234567
}
```

### 3. 获取模块统计

**接口地址**: `GET /api/v1/exam/module/statistics`

**功能描述**: 获取指定模块的统计信息

**请求参数**:
```
GET /api/v1/exam/module/statistics?session_id=mock_exam_20241128_A1B2C3D4&user_id=12345&module_type=1
```

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "mock_exam_20241128_A1B2C3D4",
        "module_type": "1",
        "statistics": {
            "total_questions": 22,
            "answered_questions": 22,
            "correct_questions": 18,
            "skipped_questions": 0,
            "average_time_per_question": 95.5,
            "total_time_seconds": 2101,
            "remaining_time_seconds": 0,
            "completion_rate": 1.0,
            "accuracy_rate": 0.818
        }
    },
    "timestamp": 1701234567
}
```

## 完整的SAT考试流程

### 1. 用户状态检查
```bash
GET /api/v1/exam/user/status?user_id=12345
```

### 2. 获取考试指引
```bash
POST /api/v1/exam/guide
{
    "user_id": 12345,
    "exam_type": "math",
    "subject": "math"
}
```

### 3. 开始第一模块
```bash
POST /api/v1/exam/module/start
{
    "session_id": "mock_exam_20241128_A1B2C3D4",
    "module_type": "1",
    "user_id": 12345
}
```

### 4. 答题过程（重复22/27次）
```bash
POST /api/v1/exam/answer/submit
{
    "session_id": "mock_exam_20241128_A1B2C3D4",
    "user_id": 12345,
    "question_id": 123456,
    "student_answer": "A",
    "time_spent_seconds": 95,
    "module_type": "1",
    "question_sequence": 1
}
```

### 5. 提交第一模块
```bash
POST /api/v1/exam/module/submit
{
    "session_id": "mock_exam_20241128_A1B2C3D4",
    "user_id": 12345,
    "module_type": "1",
    "force_submit": false
}
```

### 6. 开始第二模块（系统自动确定2E或2H）
```bash
POST /api/v1/exam/module/start
{
    "session_id": "mock_exam_20241128_A1B2C3D4",
    "module_type": "2H",  # 根据第一模块表现确定
    "user_id": 12345
}
```

### 7. 第二模块答题和提交
重复步骤4和5，但`module_type`改为"2E"或"2H"

### 8. 提交整个考试
```bash
POST /api/v1/exam/submit
{
    "session_id": "mock_exam_20241128_A1B2C3D4",
    "user_id": 12345,
    "force_submit": false
}
```

## 错误处理

常见错误码：
- `SESSION_NOT_FOUND`: 会话不存在
- `PERMISSION_DENIED`: 用户无权访问
- `MODULE_NOT_STARTED`: 模块尚未开始
- `MODULE_ALREADY_SUBMITTED`: 模块已经提交
- `SUBMIT_MODULE_FAILED`: 模块提交失败
- `INCOMPLETE_MODULE`: 模块未完成（需要force_submit=true）

## 自适应逻辑说明

### 阈值设置
- **数学**: 65%正确率进入困难模块（2H），否则进入简单模块（2E）
- **语言**: 60%正确率进入困难模块（2H），否则进入简单模块（2E）

### 自适应流程
1. 用户完成第一模块的所有题目
2. 提交第一模块时，系统计算正确率
3. 根据正确率和阈值确定第二模块类型
4. 系统自动创建第二模块的进度记录
5. 用户可以开始第二模块

### 分数计算
- **原始分数**: 答对题数
- **正确率**: 答对题数 / 总题数
- **SAT分数**: 基于正确率的简化计算（200-800分）
- **分数区间**: SAT分数 ± 30分的误差范围

## SAT考试类型和流程

### 考试类型

| 考试类型 | 说明 | 包含科目 | 总时长 |
|---------|------|---------|--------|
| Full | 全长模考 | 阅读 + 数学 | 无时间限制 |
| Math | 数学单科 | 仅数学 | 无时间限制 |
| Reading | 语言单科 | 仅阅读 | 无时间限制 |

### 模块结构

| 科目 | 模块 | 题目数量 | 时间限制 | 自适应阈值 |
|------|------|----------|----------|------------|
| 阅读 | 模块1 | 27题 | 无限制 | 60% |
| 阅读 | 模块2E (简单) | 27题 | 无限制 | - |
| 阅读 | 模块2H (困难) | 27题 | 无限制 | - |
| 数学 | 模块1 | 22题 | 无限制 | 65% |
| 数学 | 模块2E (简单) | 22题 | 无限制 | - |
| 数学 | 模块2H (困难) | 22题 | 无限制 | - |

### 全长模考流程

1. **阅读部分**
   - 阅读模块1 (27题, 32分钟)
   - 提交模块1 → 自适应评估
   - 阅读模块2E/2H (27题, 32分钟)
   - 提交模块2

2. **休息时间** (10分钟)

3. **数学部分**
   - 数学模块1 (22题, 35分钟)
   - 提交模块1 → 自适应评估
   - 数学模块2E/2H (22题, 35分钟)
   - 提交模块2

4. **考试完成** → 生成综合成绩报告

### 单科考试流程

**数学单科**:
1. 数学模块1 → 提交 → 自适应评估
2. 数学模块2E/2H → 提交
3. 生成数学成绩报告

**语言单科**:
1. 阅读模块1 → 提交 → 自适应评估
2. 阅读模块2E/2H → 提交
3. 生成阅读成绩报告

## API调用流程对照表

### 全长模考 (Full) API调用序列

| 步骤 | API接口 | 请求参数 | 说明 |
|------|---------|----------|------|
| 1 | `GET /api/v1/exam/user/status` | `user_id=123` | 检查用户状态 |
| 2 | `POST /api/v1/exam/guide` | `{exam_type: "full", subject: null}` | 获取全长考试指引 |
| 3 | `POST /api/v1/exam/module/start` | `{module_type: "1", subject: "reading"}` | 开始阅读模块1 |
| 4 | `POST /api/v1/exam/answer/submit` | 重复27次 | 阅读模块1答题 |
| 5 | `POST /api/v1/exam/module/submit` | `{module_type: "1"}` | 提交阅读模块1 |
| 6 | `POST /api/v1/exam/module/start` | `{module_type: "2E/2H"}` | 开始阅读模块2 |
| 7 | `POST /api/v1/exam/answer/submit` | 重复27次 | 阅读模块2答题 |
| 8 | `POST /api/v1/exam/module/submit` | `{module_type: "2E/2H"}` | 提交阅读模块2 |
| 9 | 休息时间 | 10分钟倒计时 | 前端处理 |
| 10 | `POST /api/v1/exam/module/start` | `{module_type: "1", subject: "math"}` | 开始数学模块1 |
| 11 | `POST /api/v1/exam/answer/submit` | 重复22次 | 数学模块1答题 |
| 12 | `POST /api/v1/exam/module/submit` | `{module_type: "1"}` | 提交数学模块1 |
| 13 | `POST /api/v1/exam/module/start` | `{module_type: "2E/2H"}` | 开始数学模块2 |
| 14 | `POST /api/v1/exam/answer/submit` | 重复22次 | 数学模块2答题 |
| 15 | `POST /api/v1/exam/module/submit` | `{module_type: "2E/2H"}` | 提交数学模块2 |
| 16 | `POST /api/v1/exam/submit` | `{session_id, user_id}` | 提交整个考试 |

### 单科考试 (Math/Reading) API调用序列

| 步骤 | API接口 | 请求参数 | 说明 |
|------|---------|----------|------|
| 1 | `GET /api/v1/exam/user/status` | `user_id=123` | 检查用户状态 |
| 2 | `POST /api/v1/exam/guide` | `{exam_type: "math", subject: "math"}` | 获取单科考试指引 |
| 3 | `POST /api/v1/exam/module/start` | `{module_type: "1"}` | 开始模块1 |
| 4 | `POST /api/v1/exam/answer/submit` | 重复22/27次 | 模块1答题 |
| 5 | `POST /api/v1/exam/module/submit` | `{module_type: "1"}` | 提交模块1 |
| 6 | `POST /api/v1/exam/module/start` | `{module_type: "2E/2H"}` | 开始模块2 |
| 7 | `POST /api/v1/exam/answer/submit` | 重复22/27次 | 模块2答题 |
| 8 | `POST /api/v1/exam/module/submit` | `{module_type: "2E/2H"}` | 提交模块2 |
| 9 | `POST /api/v1/exam/submit` | `{session_id, user_id}` | 提交整个考试 |

## 后端功能特性

### ✅ 已完成的核心功能

1. **模块提交接口** - 完整的模块提交、成绩计算、自适应逻辑
2. **科目切换逻辑** - 全长模考中阅读到数学的平滑切换
3. **自适应逻辑** - 基于第一模块表现智能确定第二模块类型
4. **错误处理** - 全面的异常情况处理和用户友好的错误信息
5. **数据一致性检查** - 多层次的数据验证确保系统稳定性

### 🔧 技术实现亮点

1. **DDD架构** - 清晰的领域驱动设计，职责分离
2. **错误恢复** - 网络异常、数据不一致等情况的优雅处理
3. **性能优化** - 批量数据处理、智能缓存策略
4. **可观测性** - 详细的日志记录和性能监控
5. **可扩展性** - 支持未来考试类型和评分算法扩展

### 📊 数据一致性保障

- **多层验证**: 题目数量、答题记录、时间数据、分数统计
- **实时检查**: 模块提交时的即时数据验证
- **全面报告**: 可选的完整一致性检查报告
- **优雅降级**: 检查失败不影响正常考试流程

### ⏱️ 时间管理特性

- **功能状态**: 时间管理功能已关闭
- **统计用途**: 仅记录答题时间用于统计，不做验证
- **无时间限制**: 不进行超时检查和时间验证
- **灵活提交**: 用户可随时提交模块，无时间约束

## 注意事项

1. **模块顺序**: 必须先完成第一模块才能开始第二模块
2. **自适应逻辑**: 只有第一模块提交时才会触发自适应逻辑
3. **强制提交**: 如果未完成所有题目，需要设置`force_submit=true`
4. **会话状态**: 提交模块不会改变会话状态，只有提交整个考试才会完成会话
5. **时间管理**: 时间管理功能已关闭，无时间限制约束
6. **科目切换**: 全长模考中，阅读部分完成后会自动切换到数学部分
7. **休息时间**: 全长模考中，阅读和数学部分之间有10分钟建议休息（非强制）
8. **自适应阈值**: 阅读60%，数学65%，达到阈值进入困难模块(2H)，否则进入简单模块(2E)
9. **数据一致性**: 系统会自动验证数据完整性，异常情况下会返回详细错误信息
10. **灵活提交**: 用户可随时提交模块，无需考虑时间因素

## 环境变量配置

- `ENABLE_CONSISTENCY_CHECK=true`: 启用详细的数据一致性检查（调试用）
