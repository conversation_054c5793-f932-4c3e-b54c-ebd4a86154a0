# SAT自适应模块设计方案（简化版）

## 问题描述

当前的考试会话创建接口返回的模块信息是固定的，每个section都包含Module1和Module2E。但根据SAT考试规则，模块2的类型应该根据模块1的表现来动态决定：
- 如果模块1表现好，进入Module2H（困难版）
- 如果模块1表现差，进入Module2E（简单版）

## 现有数据库结构分析

数据库中`t_sat_mock_exam_module_progress`表的`module_type`字段已经支持具体的模块类型：
- `reading_1`, `reading_2E`, `reading_2H`
- `math_1`, `math_2E`, `math_2H`

所以不需要新增字段，只需要调整逻辑。

## 简化设计方案

### 1. 初始化时的处理

创建会话时，只初始化模块1的进度记录：
- `reading_1`
- `math_1`

模块2的记录在模块1完成后再创建。

### 2. 接口响应结构调整

#### 新的响应结构
```json
{
  "sections": [
    {
      "section_name": "阅读",
      "subject": "reading",
      "modules": [
        {
          "module_type": "1",
          "question_count": 27,
          "time_limit_minutes": 32,
          "difficulty_level": "standard",
          "status": "available"
        },
        {
          "module_type": "2",  // 占位符，实际类型待确定
          "question_count": 27,
          "time_limit_minutes": 32,
          "difficulty_level": "adaptive",
          "status": "pending",
          "adaptive_info": {
            "depends_on": "1",
            "possible_types": ["2E", "2H"]
          }
        }
      ]
    }
  ]
}
```

### 3. 成绩保存方案

利用现有字段保存模块1成绩：
- `answered_questions`: 已答题数
- `correct_questions`: 答对题数
- 正确率 = correct_questions / answered_questions

### 4. 自适应逻辑实现

#### 4.1 模块1完成时的评估逻辑
```rust
impl AdaptiveModuleService {
    /// 评估模块1表现并创建模块2进度记录
    pub async fn evaluate_and_create_module2(
        &self,
        session_id: &str,
        subject: Subject,
    ) -> Result<ModuleType> {
        // 1. 从模块进度表获取模块1的成绩
        let module1_progress = self.progress_repository
            .find_by_session_and_module_type(session_id, &format!("{}_1", subject.to_string().to_lowercase()))
            .await?;

        // 2. 计算正确率
        let correct_rate = if module1_progress.answered_questions > 0 {
            module1_progress.correct_questions as f32 / module1_progress.answered_questions as f32
        } else {
            0.0
        };

        // 3. 根据阈值确定模块2类型
        let threshold = self.get_adaptive_threshold(subject);
        let module2_type = if correct_rate >= threshold {
            ModuleType::Module2H
        } else {
            ModuleType::Module2E
        };

        // 4. 创建模块2的进度记录
        let module2_progress = ExamModuleProgress::new(
            session_id.to_string(),
            module1_progress.user_id,
            module1_progress.paper_id,
            module2_type,
            subject,
            module2_type.question_count(subject),
            module2_type.time_limit_minutes(subject) * 60,
        );

        self.progress_repository.create(&module2_progress).await?;

        Ok(module2_type)
    }
}
```

#### 4.2 开始模块接口调整
```rust
pub async fn start_module(
    &self,
    request: StartModuleRequestDto,
) -> Result<StartModuleResponseDto> {
    // 如果是模块2，需要先检查是否已创建进度记录
    if request.module_type.is_second_module() {
        // 检查是否已有模块2的进度记录
        let existing_progress = self.check_module2_progress(&request.session_id, request.subject).await?;

        if existing_progress.is_none() {
            // 如果没有，先评估模块1并创建模块2记录
            self.adaptive_service.evaluate_and_create_module2(&request.session_id, request.subject).await?;
        }
    }

    // 继续原有的开始模块逻辑...
}
```

### 5. 实现步骤

1. **第一步**：修改会话初始化逻辑，只创建模块1的进度记录
2. **第二步**：调整DTO结构，支持自适应模块信息
3. **第三步**：实现自适应评估服务
4. **第四步**：修改开始模块逻辑，支持动态创建模块2
5. **第五步**：更新相关的查询逻辑

### 6. 数据库记录示例

#### 初始化时（只有模块1）
```
session_id: exam_123
module_type: reading_1, math_1
```

#### 模块1完成后（根据成绩创建模块2）
```
session_id: exam_123
module_type: reading_1, reading_2H, math_1, math_2E
```

### 7. 前端交互流程

1. **创建会话时**：前端收到模块2状态为"pending"
2. **模块1完成时**：后端自动评估并创建模块2记录
3. **开始模块2时**：前端请求开始模块2，后端返回具体的2E或2H内容

## 总结

这个简化方案利用现有的数据库结构，通过动态创建模块2进度记录来实现自适应逻辑，无需新增字段，实现简单且高效。
