# SAT考试系统快速测试指南

## 🚀 快速开始

### 1. 启动服务
```bash
# 在项目根目录下
cd recommendation-system2
cargo run --bin api
```

### 2. 检查测试数据
```bash
# 连接数据库并检查测试数据
psql -d your_database -f scripts/check_test_data.sql
```

### 3. 运行自动化测试
```bash
# 给脚本添加执行权限
chmod +x scripts/test_exam_flow.sh

# 运行完整流程测试
./scripts/test_exam_flow.sh
```

## 📋 测试步骤说明

### 自动化测试包含以下步骤：

1. **服务健康检查** - 确认API服务正常运行
2. **获取考试指引** - 测试考试类型和配置获取
3. **创建考试会话** - 测试会话创建和ID生成
4. **阅读模块1** - 测试模块开始和提交
5. **自适应逻辑** - 验证基于成绩的模块2类型选择
6. **阅读模块2** - 测试自适应模块的执行
7. **科目切换** - 验证从阅读到数学的切换
8. **数学模块1&2** - 测试数学部分的完整流程
9. **错误场景** - 测试重复提交、无效会话等异常情况

## 🎯 预期结果

### 成功的测试输出示例：
```
🧪 开始SAT考试系统完整流程测试
==================================
[INFO] 检查服务状态...
[SUCCESS] 服务运行正常
[INFO] 步骤1: 获取考试指引...
[SUCCESS] 考试指引获取成功
[INFO] 步骤2: 创建考试会话...
[SUCCESS] 会话创建成功: exam_20241128_001_1
[INFO] 步骤3: 开始阅读模块1...
[SUCCESS] 阅读模块1开始成功
[INFO] 步骤4: 提交阅读模块1...
[SUCCESS] 阅读模块1提交成功
[INFO] 自适应结果: 模块2类型=2E, 阈值达成=false
[INFO] 步骤5: 开始阅读模块2 (2E)...
[SUCCESS] 阅读模块2开始成功
[INFO] 提交阅读模块2...
[SUCCESS] 阅读模块2提交成功
[INFO] 下一步操作: SwitchSubject
[SUCCESS] 科目切换逻辑触发正确
[INFO] 步骤6: 开始数学模块1...
[SUCCESS] 数学模块1开始成功
[INFO] 提交数学模块1...
[SUCCESS] 数学模块1提交成功，模块2类型: 2H
[INFO] 开始数学模块2 (2H)...
[SUCCESS] 数学模块2开始成功
[INFO] 提交数学模块2...
[SUCCESS] 数学模块2提交成功
[INFO] 最终操作: CompleteExam
[SUCCESS] 考试流程完成，可以提交整个考试
[INFO] 步骤7: 测试错误场景...
[INFO] 测试重复提交模块...
[SUCCESS] 重复提交正确被拒绝
[INFO] 测试无效会话...
[SUCCESS] 无效会话正确被拒绝
==================================
[SUCCESS] 🎉 所有测试完成！

📊 测试总结:
- 会话ID: exam_20241128_001_1
- 阅读模块2类型: 2E
- 数学模块2类型: 2H
- 科目切换: ✅
- 自适应逻辑: ✅
- 错误处理: ✅

🚀 系统测试通过，可以投入使用！
```

## 🔧 手动测试

如果你想手动测试特定功能，可以使用以下curl命令：

### 创建会话
```bash
curl -X POST "http://localhost:8000/api/v1/exam/session/create" \
  -H "Content-Type: application/json" \
  -d '{"user_id": 1, "exam_type": "Full", "paper_id": 1}'
```

### 开始模块
```bash
curl -X POST "http://localhost:8000/api/v1/exam/module/start" \
  -H "Content-Type: application/json" \
  -d '{"session_id": "your_session_id", "user_id": 1, "module_type": "1"}'
```

### 提交模块
```bash
curl -X POST "http://localhost:8000/api/v1/exam/module/submit" \
  -H "Content-Type: application/json" \
  -d '{"session_id": "your_session_id", "user_id": 1, "module_type": "1", "force_submit": true}'
```

## 🐛 故障排除

### 1. 服务启动失败
```bash
# 检查端口占用
lsof -i :8000

# 检查日志
RUST_LOG=debug cargo run --bin api
```

### 2. 数据库连接问题
```bash
# 检查数据库连接
psql -h localhost -U your_user -d your_db

# 检查环境变量
echo $DATABASE_URL
```

### 3. 测试数据不足
```bash
# 运行数据检查脚本
psql -d your_database -f scripts/check_test_data.sql

# 根据输出建议添加缺失数据
```

### 4. 清理测试数据
```bash
# 清理最近的测试数据
psql -d your_database -c "
DELETE FROM exam_answers WHERE created_at > NOW() - INTERVAL '1 day';
DELETE FROM exam_module_progress WHERE created_at > NOW() - INTERVAL '1 day';
DELETE FROM exam_sessions WHERE created_at > NOW() - INTERVAL '1 day';
"
```

## 📊 测试覆盖范围

### ✅ 功能测试
- [x] 会话管理
- [x] 模块开始/提交
- [x] 自适应逻辑
- [x] 科目切换
- [x] 成绩计算
- [x] 错误处理

### ✅ 业务逻辑测试
- [x] 阅读阈值60%
- [x] 数学阈值65%
- [x] 模块类型选择(2E/2H)
- [x] 全长模考流程
- [x] 科目顺序(Reading→Math)

### ✅ 异常场景测试
- [x] 重复提交
- [x] 无效会话
- [x] 参数验证
- [x] 数据一致性

## 🎉 测试完成后

测试通过后，你的SAT考试系统就可以：

1. **接受前端调用** - 所有API接口都已验证可用
2. **处理完整考试流程** - 从创建会话到完成考试
3. **智能自适应** - 根据学生表现调整难度
4. **优雅错误处理** - 各种异常情况都有合适的响应
5. **数据一致性保障** - 确保考试数据的完整性

现在可以开始前端开发或与现有前端系统集成了！🚀
