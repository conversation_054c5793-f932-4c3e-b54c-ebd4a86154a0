# SAT考试系统完整流程测试指南

## 🎯 测试目标

测试完整的SAT考试流程，包括：
- 会话创建和管理
- 模块开始和提交
- 自适应逻辑
- 科目切换
- 数据一致性

## 📋 测试前准备

### 1. 启动服务
```bash
# 启动后端服务
cd recommendation-system2
cargo run --bin api

# 确认服务运行在 http://localhost:8000
```

### 2. 准备测试数据
确保数据库中有：
- 用户数据（user_id: 1）
- 试卷数据（paper_id: 1）
- 题目数据（足够的阅读和数学题目）

## 🧪 完整测试流程

### 阶段1: 创建考试会话

#### 1.1 获取考试指引
```bash
curl -X GET "http://localhost:8000/api/v1/exam/guide?exam_type=Full&user_id=1" \
  -H "Content-Type: application/json"
```

**期望响应：**
```json
{
  "code": 0,
  "message": "SUCCESS",
  "data": {
    "exam_type": "Full",
    "total_questions": 98,
    "total_time_minutes": 0,
    "sections": [
      {
        "section_name": "语言推理",
        "subject": "Reading",
        "modules": [...]
      },
      {
        "section_name": "数学",
        "subject": "Math", 
        "modules": [...]
      }
    ]
  }
}
```

#### 1.2 创建考试会话
```bash
curl -X POST "http://localhost:8000/api/v1/exam/session/create" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1,
    "exam_type": "Full",
    "paper_id": 1
  }'
```

**期望响应：**
```json
{
  "code": 0,
  "message": "SUCCESS", 
  "data": {
    "session_id": "exam_20241128_001_1",
    "exam_type": "Full",
    "current_subject": "Reading",
    "session_status": "InProgress"
  }
}
```

**✅ 检查点：**
- 会话ID格式正确
- 当前科目为Reading
- 状态为InProgress

### 阶段2: 阅读部分测试

#### 2.1 开始阅读模块1
```bash
curl -X POST "http://localhost:8000/api/v1/exam/module/start" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "module_type": "1"
  }'
```

**期望响应：**
```json
{
  "code": 0,
  "message": "SUCCESS",
  "data": {
    "session_id": "exam_20241128_001_1",
    "module_info": {
      "module_type": "1",
      "question_count": 27,
      "time_limit_minutes": 0,
      "status": "InProgress"
    },
    "questions": [...],
    "current_question_index": 1
  }
}
```

**✅ 检查点：**
- 返回27道阅读题目
- 模块状态为InProgress
- 题目内容完整

#### 2.2 模拟答题（可选）
```bash
# 提交几道题目的答案
curl -X POST "http://localhost:8000/api/v1/exam/answer/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "question_id": 1,
    "user_answer": "A",
    "response_time_seconds": 60
  }'
```

#### 2.3 提交阅读模块1
```bash
curl -X POST "http://localhost:8000/api/v1/exam/module/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "module_type": "1",
    "force_submit": true
  }'
```

**期望响应：**
```json
{
  "code": 0,
  "message": "SUCCESS",
  "data": {
    "module_score": {
      "raw_score": 15,
      "max_score": 27,
      "accuracy_rate": 0.556
    },
    "adaptive_result": {
      "module2_type": "2E",
      "threshold_met": false,
      "performance_score": 55.6,
      "threshold_info": {
        "threshold_value": 60.0,
        "subject": "Reading"
      }
    },
    "next_action": {
      "action_type": "StartNextModule",
      "description": "请开始阅读模块2E",
      "can_continue": true
    }
  }
}
```

**✅ 检查点：**
- 自适应逻辑正确工作（<60%进入2E）
- 下一步操作为StartNextModule
- 分数计算正确

#### 2.4 开始阅读模块2E
```bash
curl -X POST "http://localhost:8000/api/v1/exam/module/start" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "module_type": "2E"
  }'
```

#### 2.5 提交阅读模块2E
```bash
curl -X POST "http://localhost:8000/api/v1/exam/module/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "module_type": "2E",
    "force_submit": true
  }'
```

**期望响应：**
```json
{
  "code": 0,
  "message": "SUCCESS",
  "data": {
    "next_action": {
      "action_type": "SwitchSubject",
      "description": "语言部分已完成，休息10分钟后开始数学部分",
      "can_continue": true,
      "suggested_wait_seconds": 600
    }
  }
}
```

**✅ 检查点：**
- 下一步操作为SwitchSubject
- 建议休息10分钟
- 科目切换逻辑触发

### 阶段3: 科目切换验证

#### 3.1 检查会话状态
```bash
curl -X GET "http://localhost:8000/api/v1/exam/session/status?session_id=exam_20241128_001_1&user_id=1"
```

**期望响应：**
```json
{
  "code": 0,
  "message": "SUCCESS",
  "data": {
    "session_id": "exam_20241128_001_1",
    "current_subject": "Math",
    "session_status": "InProgress"
  }
}
```

**✅ 检查点：**
- 当前科目已切换为Math
- 会话状态仍为InProgress

### 阶段4: 数学部分测试

#### 4.1 开始数学模块1
```bash
curl -X POST "http://localhost:8000/api/v1/exam/module/start" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "module_type": "1"
  }'
```

**期望响应：**
```json
{
  "data": {
    "module_info": {
      "question_count": 22,
      "subject": "Math"
    },
    "questions": [...]
  }
}
```

**✅ 检查点：**
- 返回22道数学题目
- 科目正确为Math

#### 4.2 提交数学模块1（高分测试）
```bash
curl -X POST "http://localhost:8000/api/v1/exam/module/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "module_type": "1",
    "force_submit": true
  }'
```

**期望响应（如果分数>65%）：**
```json
{
  "data": {
    "adaptive_result": {
      "module2_type": "2H",
      "threshold_met": true,
      "performance_score": 72.7,
      "threshold_info": {
        "threshold_value": 65.0,
        "subject": "Math"
      }
    }
  }
}
```

**✅ 检查点：**
- 数学阈值65%正确应用
- 高分进入2H模块

#### 4.3 完成数学模块2H
```bash
# 开始模块2H
curl -X POST "http://localhost:8000/api/v1/exam/module/start" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "module_type": "2H"
  }'

# 提交模块2H
curl -X POST "http://localhost:8000/api/v1/exam/module/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "module_type": "2H",
    "force_submit": true
  }'
```

**期望响应：**
```json
{
  "data": {
    "next_action": {
      "action_type": "CompleteExam",
      "description": "数学部分已完成，可以提交整个考试",
      "can_continue": true
    }
  }
}
```

**✅ 检查点：**
- 下一步操作为CompleteExam
- 全长模考流程完成

## 🔍 错误场景测试

### 1. 重复提交测试
```bash
# 尝试重复提交已提交的模块
curl -X POST "http://localhost:8000/api/v1/exam/module/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "exam_20241128_001_1",
    "user_id": 1,
    "module_type": "1",
    "force_submit": false
  }'
```

**期望响应：**
```json
{
  "code": -1,
  "message": "VALIDATION_ERROR",
  "data": {
    "error": "模块已经提交，无法重复提交。如需重新提交，请联系管理员。"
  }
}
```

### 2. 无效会话测试
```bash
curl -X POST "http://localhost:8000/api/v1/exam/module/start" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "invalid_session",
    "user_id": 1,
    "module_type": "1"
  }'
```

**期望响应：**
```json
{
  "code": -1,
  "message": "NOT_FOUND",
  "data": {
    "error": "会话不存在"
  }
}
```

## 📊 测试检查清单

### ✅ 功能测试
- [ ] 会话创建成功
- [ ] 模块开始正常
- [ ] 答题提交正常
- [ ] 模块提交成功
- [ ] 自适应逻辑正确（阅读60%，数学65%）
- [ ] 科目切换正常
- [ ] 错误处理正确

### ✅ 数据一致性
- [ ] 会话状态正确更新
- [ ] 模块进度正确记录
- [ ] 答题记录完整
- [ ] 分数计算准确

### ✅ 边界情况
- [ ] 重复操作处理
- [ ] 无效参数处理
- [ ] 网络异常恢复

## 🐛 常见问题排查

### 1. 服务启动失败
```bash
# 检查端口占用
lsof -i :8000

# 检查数据库连接
psql -h localhost -U your_user -d your_db
```

### 2. 数据库数据不足
```bash
# 检查题目数量
SELECT subject, COUNT(*) FROM questions GROUP BY subject;

# 检查试卷配置
SELECT * FROM sat_papers WHERE id = 1;
```

### 3. 日志查看
```bash
# 查看详细日志
RUST_LOG=debug cargo run --bin api
```

## 🎯 成功标准

测试通过的标准：
1. 所有API调用返回正确状态码
2. 自适应逻辑按阈值正确工作
3. 科目切换在正确时机触发
4. 错误情况返回友好提示
5. 数据一致性检查通过

完成以上测试后，你的SAT考试系统就可以投入使用了！🚀
