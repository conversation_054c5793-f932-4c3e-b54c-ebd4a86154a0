#!/usr/bin/env python3
"""
完整的考试会话工作流测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def print_section(title):
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_result(success, message):
    status = "✅ 成功" if success else "❌ 失败"
    print(f"{status}: {message}")

def test_create_session(user_id, exam_type, expected_success=True):
    """测试创建会话"""
    url = f"{BASE_URL}/exam/session/create"
    payload = {
        "user_id": user_id,
        "paper_id": 700166615844655104,
        "exam_type": exam_type
    }
    
    try:
        response = requests.post(url, json=payload)
        data = response.json()
        
        if expected_success:
            if data.get("code") == 0:
                session_data = data.get("data", {})
                print_result(True, f"用户{user_id}创建{exam_type}会话成功")
                print(f"   会话ID: {session_data.get('session_id')}")
                print(f"   考试类型: {session_data.get('exam_type')}")
                print(f"   总题数: {session_data.get('total_questions')}")
                print(f"   总时间: {session_data.get('total_time_minutes')}分钟")
                return session_data.get('session_id')
            else:
                print_result(False, f"创建会话失败: {data.get('message')}")
                return None
        else:
            if data.get("code") != 0:
                print_result(True, f"预期失败成功: {data.get('message')}")
                return None
            else:
                print_result(False, "预期失败但实际成功了")
                return data.get("data", {}).get('session_id')
                
    except Exception as e:
        print_result(False, f"请求异常: {e}")
        return None

def test_cancel_session(session_id, user_id):
    """测试取消会话"""
    url = f"{BASE_URL}/exam/session/cancel"
    payload = {
        "session_id": session_id,
        "user_id": user_id
    }
    
    try:
        response = requests.post(url, json=payload)
        data = response.json()
        
        if data.get("code") == 0:
            print_result(True, f"取消会话成功: {data.get('data')}")
            return True
        else:
            print_result(False, f"取消会话失败: {data.get('message')}")
            return False
            
    except Exception as e:
        print_result(False, f"请求异常: {e}")
        return False

def main():
    print("SAT考试系统完整工作流测试")
    
    # 测试场景1：新用户创建会话
    print_section("场景1：新用户创建不同类型的会话")
    
    # 用户A创建full类型会话
    session_a = test_create_session(11111, "full")
    
    # 用户B创建math类型会话
    session_b = test_create_session(22222, "math")
    
    # 用户C创建reading类型会话
    session_c = test_create_session(33333, "reading")
    
    # 测试场景2：重复创建相同类型会话
    print_section("场景2：重复创建相同类型会话（应该返回已存在会话）")
    
    test_create_session(11111, "full")  # 应该返回已存在会话
    
    # 测试场景3：尝试创建不同类型会话
    print_section("场景3：尝试创建不同类型会话（应该失败）")
    
    test_create_session(11111, "math", expected_success=False)  # 应该失败
    
    # 测试场景4：取消会话
    print_section("场景4：取消会话")
    
    if session_a:
        test_cancel_session(session_a, 11111)
    
    # 测试场景5：取消会话后创建新类型会话
    print_section("场景5：取消会话后创建新类型会话")
    
    test_create_session(11111, "math")  # 现在应该成功
    
    # 测试场景6：错误处理
    print_section("场景6：错误处理测试")
    
    # 尝试取消不存在的会话
    test_cancel_session("invalid_session_id", 11111)
    
    # 尝试取消别人的会话
    if session_b:
        test_cancel_session(session_b, 99999)  # 错误的用户ID
    
    print_section("测试完成")
    print("所有测试场景已执行完毕！")

if __name__ == "__main__":
    main()
