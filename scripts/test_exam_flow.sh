#!/bin/bash

# SAT考试系统快速测试脚本
# 使用方法: ./scripts/test_exam_flow.sh

set -e

# 配置
BASE_URL="http://localhost:8080"
USER_ID=1
PAPER_ID=1

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        log_success "服务运行正常"
    else
        log_error "服务未运行，请先启动服务: cargo run --bin api"
        exit 1
    fi
}

# 提取JSON字段值
extract_json_field() {
    echo "$1" | grep -o "\"$2\":[^,}]*" | cut -d':' -f2 | tr -d '"' | tr -d ' '
}

# 测试步骤1: 获取考试指引
test_exam_guide() {
    log_info "步骤1: 获取考试指引..."
    
    response=$(curl -s -X GET "$BASE_URL/api/v1/exam/guide?exam_type=Full&user_id=$USER_ID")
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" = "0" ]; then
        log_success "考试指引获取成功"
        return 0
    else
        log_error "考试指引获取失败: $response"
        return 1
    fi
}

# 测试步骤2: 创建考试会话
test_create_session() {
    log_info "步骤2: 创建考试会话..."
    
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/session/create" \
        -H "Content-Type: application/json" \
        -d "{\"user_id\": $USER_ID, \"exam_type\": \"Full\", \"paper_id\": $PAPER_ID}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" = "0" ]; then
        SESSION_ID=$(extract_json_field "$response" "session_id")
        log_success "会话创建成功: $SESSION_ID"
        return 0
    else
        log_error "会话创建失败: $response"
        return 1
    fi
}

# 测试步骤3: 开始阅读模块1
test_start_reading_module1() {
    log_info "步骤3: 开始阅读模块1..."
    
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/start" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"$SESSION_ID\", \"user_id\": $USER_ID, \"module_type\": \"1\"}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" = "0" ]; then
        log_success "阅读模块1开始成功"
        return 0
    else
        log_error "阅读模块1开始失败: $response"
        return 1
    fi
}

# 测试步骤4: 提交阅读模块1
test_submit_reading_module1() {
    log_info "步骤4: 提交阅读模块1..."
    
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/submit" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"$SESSION_ID\", \"user_id\": $USER_ID, \"module_type\": \"1\", \"force_submit\": true}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" = "0" ]; then
        MODULE2_TYPE=$(extract_json_field "$response" "module2_type")
        THRESHOLD_MET=$(extract_json_field "$response" "threshold_met")
        log_success "阅读模块1提交成功"
        log_info "自适应结果: 模块2类型=$MODULE2_TYPE, 阈值达成=$THRESHOLD_MET"
        return 0
    else
        log_error "阅读模块1提交失败: $response"
        return 1
    fi
}

# 测试步骤5: 开始并提交阅读模块2
test_reading_module2() {
    log_info "步骤5: 开始阅读模块2 ($MODULE2_TYPE)..."
    
    # 开始模块2
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/start" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"$SESSION_ID\", \"user_id\": $USER_ID, \"module_type\": \"$MODULE2_TYPE\"}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" != "0" ]; then
        log_error "阅读模块2开始失败: $response"
        return 1
    fi
    
    log_success "阅读模块2开始成功"
    
    # 提交模块2
    log_info "提交阅读模块2..."
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/submit" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"$SESSION_ID\", \"user_id\": $USER_ID, \"module_type\": \"$MODULE2_TYPE\", \"force_submit\": true}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" = "0" ]; then
        ACTION_TYPE=$(extract_json_field "$response" "action_type")
        log_success "阅读模块2提交成功"
        log_info "下一步操作: $ACTION_TYPE"
        
        if [ "$ACTION_TYPE" = "SwitchSubject" ]; then
            log_success "科目切换逻辑触发正确"
        else
            log_warning "预期下一步操作为SwitchSubject，实际为: $ACTION_TYPE"
        fi
        return 0
    else
        log_error "阅读模块2提交失败: $response"
        return 1
    fi
}

# 测试步骤6: 数学部分测试
test_math_modules() {
    log_info "步骤6: 开始数学模块1..."
    
    # 开始数学模块1
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/start" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"$SESSION_ID\", \"user_id\": $USER_ID, \"module_type\": \"1\"}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" != "0" ]; then
        log_error "数学模块1开始失败: $response"
        return 1
    fi
    
    log_success "数学模块1开始成功"
    
    # 提交数学模块1
    log_info "提交数学模块1..."
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/submit" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"$SESSION_ID\", \"user_id\": $USER_ID, \"module_type\": \"1\", \"force_submit\": true}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" != "0" ]; then
        log_error "数学模块1提交失败: $response"
        return 1
    fi
    
    MATH_MODULE2_TYPE=$(extract_json_field "$response" "module2_type")
    log_success "数学模块1提交成功，模块2类型: $MATH_MODULE2_TYPE"
    
    # 开始并提交数学模块2
    log_info "开始数学模块2 ($MATH_MODULE2_TYPE)..."
    
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/start" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"$SESSION_ID\", \"user_id\": $USER_ID, \"module_type\": \"$MATH_MODULE2_TYPE\"}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" != "0" ]; then
        log_error "数学模块2开始失败: $response"
        return 1
    fi
    
    log_success "数学模块2开始成功"
    
    # 提交数学模块2
    log_info "提交数学模块2..."
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/submit" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"$SESSION_ID\", \"user_id\": $USER_ID, \"module_type\": \"$MATH_MODULE2_TYPE\", \"force_submit\": true}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" = "0" ]; then
        FINAL_ACTION=$(extract_json_field "$response" "action_type")
        log_success "数学模块2提交成功"
        log_info "最终操作: $FINAL_ACTION"
        
        if [ "$FINAL_ACTION" = "CompleteExam" ]; then
            log_success "考试流程完成，可以提交整个考试"
        else
            log_warning "预期最终操作为CompleteExam，实际为: $FINAL_ACTION"
        fi
        return 0
    else
        log_error "数学模块2提交失败: $response"
        return 1
    fi
}

# 测试错误场景
test_error_scenarios() {
    log_info "步骤7: 测试错误场景..."
    
    # 测试重复提交
    log_info "测试重复提交模块..."
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/submit" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"$SESSION_ID\", \"user_id\": $USER_ID, \"module_type\": \"1\", \"force_submit\": false}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" != "0" ]; then
        log_success "重复提交正确被拒绝"
    else
        log_warning "重复提交应该被拒绝，但成功了"
    fi
    
    # 测试无效会话
    log_info "测试无效会话..."
    response=$(curl -s -X POST "$BASE_URL/api/v1/exam/module/start" \
        -H "Content-Type: application/json" \
        -d "{\"session_id\": \"invalid_session\", \"user_id\": $USER_ID, \"module_type\": \"1\"}")
    
    code=$(extract_json_field "$response" "code")
    
    if [ "$code" != "0" ]; then
        log_success "无效会话正确被拒绝"
    else
        log_warning "无效会话应该被拒绝，但成功了"
    fi
}

# 主测试流程
main() {
    echo "🧪 开始SAT考试系统完整流程测试"
    echo "=================================="
    
    # 检查服务
    check_service || exit 1
    
    # 执行测试步骤
    test_exam_guide || exit 1
    test_create_session || exit 1
    test_start_reading_module1 || exit 1
    test_submit_reading_module1 || exit 1
    test_reading_module2 || exit 1
    test_math_modules || exit 1
    test_error_scenarios
    
    echo "=================================="
    log_success "🎉 所有测试完成！"
    echo ""
    echo "📊 测试总结:"
    echo "- 会话ID: $SESSION_ID"
    echo "- 阅读模块2类型: $MODULE2_TYPE"
    echo "- 数学模块2类型: $MATH_MODULE2_TYPE"
    echo "- 科目切换: ✅"
    echo "- 自适应逻辑: ✅"
    echo "- 错误处理: ✅"
    echo ""
    echo "🚀 系统测试通过，可以投入使用！"
}

# 运行主函数
main "$@"
