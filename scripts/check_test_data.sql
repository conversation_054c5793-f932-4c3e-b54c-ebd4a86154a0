-- SAT考试系统测试数据检查脚本
-- 使用方法: psql -d your_database -f scripts/check_test_data.sql

\echo '🔍 检查SAT考试系统测试数据...'
\echo '=================================='

-- 1. 检查用户数据
\echo '📊 1. 用户数据检查:'
SELECT 
    COUNT(*) as user_count,
    MIN(id) as min_user_id,
    MAX(id) as max_user_id
FROM users 
WHERE id <= 10;

\echo ''
\echo '用户详情 (前5个):'
SELECT id, username, created_at 
FROM users 
WHERE id <= 5 
ORDER BY id;

-- 2. 检查试卷数据
\echo ''
\echo '📋 2. 试卷数据检查:'
SELECT 
    id,
    name,
    exam_type,
    total_questions,
    created_at
FROM sat_papers 
WHERE id <= 5
ORDER BY id;

-- 3. 检查题目数据
\echo ''
\echo '📝 3. 题目数据统计:'
SELECT 
    subject_id,
    CASE 
        WHEN subject_id = 1 THEN 'Math'
        WHEN subject_id = 14 THEN 'Reading'
        ELSE 'Other'
    END as subject_name,
    COUNT(*) as question_count,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count
FROM questions 
WHERE subject_id IN (1, 14)
GROUP BY subject_id
ORDER BY subject_id;

-- 4. 检查试卷题目关联
\echo ''
\echo '🔗 4. 试卷题目关联检查:'
SELECT 
    spq.paper_id,
    q.subject_id,
    CASE 
        WHEN q.subject_id = 1 THEN 'Math'
        WHEN q.subject_id = 14 THEN 'Reading'
        ELSE 'Other'
    END as subject_name,
    COUNT(*) as question_count
FROM sat_paper_questions spq
JOIN questions q ON spq.question_id = q.id
WHERE spq.paper_id <= 5
GROUP BY spq.paper_id, q.subject_id
ORDER BY spq.paper_id, q.subject_id;

-- 5. 检查题目内容完整性
\echo ''
\echo '📖 5. 题目内容完整性检查:'
SELECT 
    subject_id,
    CASE 
        WHEN subject_id = 1 THEN 'Math'
        WHEN subject_id = 14 THEN 'Reading'
        ELSE 'Other'
    END as subject_name,
    COUNT(*) as total_questions,
    COUNT(CASE WHEN question_text IS NOT NULL AND question_text != '' THEN 1 END) as has_text,
    COUNT(CASE WHEN correct_answer IS NOT NULL AND correct_answer != '' THEN 1 END) as has_answer,
    COUNT(CASE WHEN options IS NOT NULL THEN 1 END) as has_options
FROM questions 
WHERE subject_id IN (1, 14) AND status = 'active'
GROUP BY subject_id
ORDER BY subject_id;

-- 6. 检查现有考试会话（如果有）
\echo ''
\echo '🎯 6. 现有考试会话检查:'
SELECT 
    COUNT(*) as session_count,
    COUNT(CASE WHEN session_status = 'InProgress' THEN 1 END) as in_progress,
    COUNT(CASE WHEN session_status = 'Completed' THEN 1 END) as completed
FROM exam_sessions 
WHERE created_at > NOW() - INTERVAL '1 day';

-- 7. 检查模块进度记录
\echo ''
\echo '📈 7. 模块进度记录检查:'
SELECT 
    COUNT(*) as progress_count,
    COUNT(DISTINCT session_id) as unique_sessions,
    COUNT(CASE WHEN module_status = 'InProgress' THEN 1 END) as in_progress,
    COUNT(CASE WHEN module_status = 'Completed' THEN 1 END) as completed,
    COUNT(CASE WHEN module_status = 'Submitted' THEN 1 END) as submitted
FROM exam_module_progress 
WHERE created_at > NOW() - INTERVAL '1 day';

-- 8. 检查答题记录
\echo ''
\echo '✍️ 8. 答题记录检查:'
SELECT 
    COUNT(*) as answer_count,
    COUNT(DISTINCT session_id) as unique_sessions,
    COUNT(CASE WHEN is_correct = true THEN 1 END) as correct_answers,
    COUNT(CASE WHEN is_correct = false THEN 1 END) as incorrect_answers,
    COUNT(CASE WHEN is_correct IS NULL THEN 1 END) as unanswered
FROM exam_answers 
WHERE created_at > NOW() - INTERVAL '1 day';

-- 9. 数据完整性建议
\echo ''
\echo '💡 9. 数据准备建议:'
\echo '==================='

-- 检查是否有足够的数学题目
DO $$
DECLARE
    math_count INTEGER;
    reading_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO math_count FROM questions WHERE subject_id = 1 AND status = 'active';
    SELECT COUNT(*) INTO reading_count FROM questions WHERE subject_id = 14 AND status = 'active';
    
    RAISE NOTICE '';
    RAISE NOTICE '题目数量检查:';
    RAISE NOTICE '- 数学题目: % 道 (建议至少44道)', math_count;
    RAISE NOTICE '- 阅读题目: % 道 (建议至少54道)', reading_count;
    
    IF math_count < 44 THEN
        RAISE NOTICE '⚠️  数学题目不足，建议添加更多题目';
    ELSE
        RAISE NOTICE '✅ 数学题目充足';
    END IF;
    
    IF reading_count < 54 THEN
        RAISE NOTICE '⚠️  阅读题目不足，建议添加更多题目';
    ELSE
        RAISE NOTICE '✅ 阅读题目充足';
    END IF;
END $$;

-- 检查试卷配置
DO $$
DECLARE
    paper_count INTEGER;
    paper_questions INTEGER;
BEGIN
    SELECT COUNT(*) INTO paper_count FROM sat_papers WHERE id = 1;
    
    IF paper_count = 0 THEN
        RAISE NOTICE '';
        RAISE NOTICE '⚠️  未找到ID=1的试卷，请创建测试试卷:';
        RAISE NOTICE 'INSERT INTO sat_papers (id, name, exam_type, total_questions) VALUES (1, ''测试试卷'', ''Full'', 98);';
    ELSE
        SELECT COUNT(*) INTO paper_questions FROM sat_paper_questions WHERE paper_id = 1;
        RAISE NOTICE '';
        RAISE NOTICE '试卷配置检查:';
        RAISE NOTICE '- 试卷ID=1: 存在';
        RAISE NOTICE '- 关联题目数: %', paper_questions;
        
        IF paper_questions < 98 THEN
            RAISE NOTICE '⚠️  试卷关联题目不足，建议添加更多题目关联';
        ELSE
            RAISE NOTICE '✅ 试卷题目配置充足';
        END IF;
    END IF;
END $$;

-- 检查用户数据
DO $$
DECLARE
    user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM users WHERE id = 1;
    
    RAISE NOTICE '';
    RAISE NOTICE '用户数据检查:';
    IF user_count = 0 THEN
        RAISE NOTICE '⚠️  未找到ID=1的用户，请创建测试用户:';
        RAISE NOTICE 'INSERT INTO users (id, username, email) VALUES (1, ''test_user'', ''<EMAIL>'');';
    ELSE
        RAISE NOTICE '✅ 测试用户存在';
    END IF;
END $$;

\echo ''
\echo '🎯 测试准备检查完成！'
\echo ''
\echo '如果所有检查都通过，可以运行测试脚本:'
\echo './scripts/test_exam_flow.sh'
\echo ''
\echo '如果需要清理测试数据，可以运行:'
\echo 'DELETE FROM exam_answers WHERE created_at > NOW() - INTERVAL ''1 day'';'
\echo 'DELETE FROM exam_module_progress WHERE created_at > NOW() - INTERVAL ''1 day'';'
\echo 'DELETE FROM exam_sessions WHERE created_at > NOW() - INTERVAL ''1 day'';'
