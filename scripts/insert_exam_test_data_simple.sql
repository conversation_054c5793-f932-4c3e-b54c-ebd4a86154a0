-- SAT考试记录测试数据插入脚本
-- 只插入 t_sat_mock_exam_history 表的测试数据
-- 使用试卷ID: 700540150387707904

-- 清理现有测试数据（可选）
-- DELETE FROM t_sat_mock_exam_history WHERE paper_id = 700540150387707904;

-- 插入模考历史记录测试数据
INSERT INTO t_sat_mock_exam_history (
    session_id, user_id, paper_id, paper_name, exam_type,
    total_score, reading_score, math_score, max_possible_score,
    total_questions, answered_questions, correct_questions, skipped_questions, accuracy_rate,
    total_time_seconds, reading_time_seconds, math_time_seconds, avg_response_time_seconds,
    reading_total_questions, reading_correct_questions, reading_accuracy_rate,
    math_total_questions, math_correct_questions, math_accuracy_rate,
    exam_status, completion_percentage,
    started_at, completed_at, created_at, updated_at
) VALUES 
-- 用户1的考试记录
('session_101', 1, 700540150387707904, 'SAT官方模考卷1', 'full',
 1420, 720, 700, 1600,
 98, 95, 78, 3, 0.8211,
 7500, 3900, 3600, 78.95,
 54, 42, 0.7778,
 44, 36, 0.8182,
 'completed', 96.94,
 '2024-01-20 09:00:00+00', '2024-01-20 11:05:00+00', '2024-01-20 11:05:00+00', '2024-01-20 11:05:00+00'),

('session_102', 1, 700540150387707904, 'SAT官方模考卷1', 'full',
 1380, 680, 700, 1600,
 98, 96, 76, 2, 0.7917,
 7200, 3600, 3600, 75.00,
 54, 40, 0.7407,
 44, 36, 0.8182,
 'completed', 97.96,
 '2024-01-22 14:00:00+00', '2024-01-22 16:00:00+00', '2024-01-22 16:00:00+00', '2024-01-22 16:00:00+00'),

('session_103', 1, 700540150387707904, 'SAT官方模考卷1', 'full',
 1450, 750, 700, 1600,
 98, 97, 82, 1, 0.8454,
 7800, 4200, 3600, 80.41,
 54, 45, 0.8333,
 44, 37, 0.8409,
 'completed', 98.98,
 '2024-01-25 10:00:00+00', '2024-01-25 12:05:00+00', '2024-01-25 12:05:00+00', '2024-01-25 12:05:00+00'),

('session_104', 1, 700540150387707904, 'SAT官方模考卷1', 'full',
 1520, 780, 740, 1600,
 98, 96, 84, 2, 0.8750,
 7200, 3600, 3600, 75.00,
 54, 46, 0.8519,
 44, 38, 0.8636,
 'completed', 97.96,
 '2024-02-01 09:30:00+00', '2024-02-01 11:30:00+00', '2024-02-01 11:30:00+00', '2024-02-01 11:30:00+00'),

('session_105', 1, 700540150387707904, 'SAT官方模考卷1', 'full',
 1480, 740, 740, 1600,
 98, 95, 80, 3, 0.8421,
 7020, 3420, 3600, 73.89,
 54, 43, 0.7963,
 44, 37, 0.8409,
 'completed', 96.94,
 '2024-02-05 14:00:00+00', '2024-02-05 15:57:00+00', '2024-02-05 15:57:00+00', '2024-02-05 15:57:00+00'),

-- 用户2的考试记录
('session_106', 2, 700540150387707904, 'SAT官方模考卷1', 'full',
 1350, 650, 700, 1600,
 98, 94, 72, 4, 0.7660,
 7800, 4200, 3600, 82.98,
 54, 38, 0.7037,
 44, 34, 0.7727,
 'completed', 95.92,
 '2024-01-18 10:00:00+00', '2024-01-18 12:10:00+00', '2024-01-18 12:10:00+00', '2024-01-18 12:10:00+00'),

('session_107', 2, 700540150387707904, 'SAT官方模考卷1', 'full',
 1400, 680, 720, 1600,
 98, 96, 76, 2, 0.7917,
 7500, 3900, 3600, 78.13,
 54, 40, 0.7407,
 44, 36, 0.8182,
 'completed', 97.96,
 '2024-02-08 09:00:00+00', '2024-02-08 11:05:00+00', '2024-02-08 11:05:00+00', '2024-02-08 11:05:00+00'),

-- 用户3的考试记录
('session_108', 3, 700540150387707904, 'SAT官方模考卷1', 'full',
 1300, 600, 700, 1600,
 98, 92, 68, 6, 0.7391,
 8100, 4500, 3600, 88.04,
 54, 35, 0.6481,
 44, 33, 0.7500,
 'completed', 93.88,
 '2024-01-28 13:00:00+00', '2024-01-28 15:15:00+00', '2024-01-28 15:15:00+00', '2024-01-28 15:15:00+00')

ON CONFLICT (session_id) DO UPDATE SET
    total_score = EXCLUDED.total_score,
    reading_score = EXCLUDED.reading_score,
    math_score = EXCLUDED.math_score,
    accuracy_rate = EXCLUDED.accuracy_rate,
    completed_at = EXCLUDED.completed_at,
    updated_at = EXCLUDED.updated_at;

-- 验证插入的数据
SELECT 
    '=== 插入的测试数据 ===' as info,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_id) as unique_users,
    paper_id
FROM t_sat_mock_exam_history 
WHERE paper_id = 700540150387707904
GROUP BY paper_id;

-- 显示插入的数据样本
SELECT 
    user_id, session_id, total_score, completed_at
FROM t_sat_mock_exam_history 
WHERE paper_id = 700540150387707904
ORDER BY user_id, completed_at;
