//! 考试模块处理器
//!
//! 处理考试模块相关的HTTP请求

use std::sync::Arc;
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::{Deserialize, Serialize};
use tracing::{info, error};

use recommendation_core::models::ErrorCode;
use crate::http::response;
use recommendation_core::application::exam::{
    ExamApplicationService,
    StartModuleRequestDto,
};
use recommendation_core::domain::exam::value_objects::ModuleType;

/// 开始模块请求参数
#[derive(Debug, Deserialize)]
pub struct StartModuleRequest {
    /// 会话ID
    pub session_id: String,
    /// 模块类型
    pub module_type: ModuleType,
    /// 用户ID
    pub user_id: i64,
}

/// 开始模块
///
/// POST /api/v1/exam/module/start
pub async fn start_module(
    request: web::Json<StartModuleRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到开始模块请求: 会话ID={}, 模块类型={}", 
          request.session_id, request.module_type);

    // 构建应用服务请求
    let app_request = StartModuleRequestDto {
        session_id: request.session_id.clone(),
        module_type: request.module_type,
        user_id: request.user_id,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.start_module(app_request).await {
        Ok(response) => {
            info!("成功开始模块: 会话ID={}, 模块类型={}, 题目数量={}",
                  request.session_id, request.module_type, response.questions.len());
            Ok(response::success(response))
        }
        Err(e) => {
            error!("开始模块失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("开始模块失败: {}", e)),
            ))
        }
    }
}

/// 获取会话状态
///
/// GET /api/v1/exam/session/state?session_id={session_id}&user_id={user_id}
#[derive(Debug, Deserialize)]
pub struct GetSessionStateQuery {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
}

pub async fn get_session_state(
    query: web::Query<GetSessionStateQuery>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到获取会话状态请求: 会话ID={}, 用户ID={}", 
          query.session_id, query.user_id);

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.get_session_state(&query.session_id, query.user_id).await {
        Ok(response) => {
            info!("成功获取会话状态: 会话ID={}", query.session_id);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("获取会话状态失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("获取会话状态失败: {}", e)),
            ))
        }
    }
}

/// 验证会话
///
/// GET /api/v1/exam/session/validate?session_id={session_id}&user_id={user_id}
#[derive(Debug, Deserialize)]
pub struct ValidateSessionQuery {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
}

#[derive(Debug, Serialize)]
pub struct ValidateSessionResponse {
    /// 会话是否有效
    pub is_valid: bool,
    /// 验证消息
    pub message: String,
}

pub async fn validate_session(
    query: web::Query<ValidateSessionQuery>,
    exam_service: web::Data<dyn ExamApplicationService>,
) -> ActixResult<HttpResponse> {
    info!("收到验证会话请求: 会话ID={}, 用户ID={}", 
          query.session_id, query.user_id);

    // 调用应用服务
    match exam_service.validate_session(&query.session_id, query.user_id).await {
        Ok(is_valid) => {
            let response = ValidateSessionResponse {
                is_valid,
                message: if is_valid { 
                    "会话有效".to_string() 
                } else { 
                    "会话无效或已过期".to_string() 
                },
            };
            
            info!("会话验证结果: 会话ID={}, 有效={}", query.session_id, is_valid);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("验证会话失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("验证会话失败: {}", e)),
            ))
        }
    }
}
