//! 考试记录HTTP处理器
//!
//! 提供考试记录相关的HTTP API接口

use std::sync::Arc;
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::{Deserialize, Serialize};
use tracing::{info, error};

use recommendation_core::application::exam::service_impl::{ExamRecordServiceImpl, ExamRecordApplicationService};
use recommendation_core::application::exam::dto::{
    GetExamRecordsRequestDto, GetPaperExamRecordsRequestDto,
};
use recommendation_core::infrastructure::persistence::database::manager::StorageManager;
use recommendation_core::models::ErrorCode;

use crate::http::response;

/// 获取考试记录查询参数
#[derive(Debug, Deserialize)]
pub struct GetExamRecordsQuery {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID（可选）
    pub paper_id: Option<i64>,
    /// 考试类型（可选）
    pub exam_type: Option<String>,
    /// 页码（从1开始）
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
}

/// 考试记录响应
#[derive(Debug, Serialize)]
pub struct ExamRecordsResponse {
    /// 考试记录统计列表
    pub paper_statistics: Vec<PaperStatisticsResponse>,
    /// 分页信息
    pub pagination: PaginationResponse,
}

/// 试卷统计响应
#[derive(Debug, Serialize)]
pub struct PaperStatisticsResponse {
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: String,
    /// 考试次数
    pub exam_count: u32,
    /// 完成次数
    pub completed_count: u32,
    /// 最高分
    pub best_score: Option<u32>,
    /// 最新分数
    pub latest_score: Option<u32>,
    /// 平均分
    pub average_score: Option<f64>,
    /// 最近考试详情
    pub last_exam_detail: Option<LastExamDetailResponse>,
    /// 进步趋势
    pub progress_trend: ProgressTrendResponse,
}

/// 最近考试详情响应
#[derive(Debug, Serialize)]
pub struct LastExamDetailResponse {
    /// 会话ID
    pub session_id: String,
    /// 总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
    /// 考试进度
    pub exam_progress: Option<f64>,
    /// 考试状态
    pub exam_status: String,
}

/// 进步趋势响应
#[derive(Debug, Serialize)]
pub struct ProgressTrendResponse {
    /// 是否在进步
    pub is_improving: bool,
    /// 分数变化
    pub score_change: i32,
    /// 趋势描述
    pub trend_description: String,
}

/// 分页信息响应
#[derive(Debug, Serialize)]
pub struct PaginationResponse {
    /// 当前页码
    pub current_page: u32,
    /// 每页大小
    pub page_size: u32,
    /// 总记录数
    pub total_count: u64,
    /// 总页数
    pub total_pages: u32,
}

/// 获取用户考试记录
///
/// 返回用户的考试记录统计信息，包括每个试卷的考试次数、分数统计、进步趋势等
pub async fn get_exam_records(
    params: web::Query<GetExamRecordsQuery>,
    storage: web::Data<Arc<StorageManager>>,
) -> ActixResult<HttpResponse> {
    info!("获取考试记录请求: user_id={}, paper_id={:?}, exam_type={:?}",
          params.user_id, params.paper_id, params.exam_type);

    // 创建考试记录服务
    let exam_record_service = ExamRecordServiceImpl::new(storage.get_ref().clone());

    // 构建请求DTO
    let request = GetExamRecordsRequestDto {
        user_id: params.user_id,
        paper_id: params.paper_id,
        exam_type: params.exam_type.clone(),
        page: params.page.map(|p| p as u64),
        page_size: params.page_size.map(|p| p as u64),
    };

    // 调用应用服务
    match exam_record_service.get_exam_records(request).await {
        Ok(response) => {
            info!("考试记录查询成功: user_id={}, 记录数={}",
                  params.user_id, response.paper_statistics.len());

            // 转换为HTTP响应格式
            let http_response = ExamRecordsResponse {
                paper_statistics: response.paper_statistics.into_iter().map(|stat| {
                    PaperStatisticsResponse {
                        paper_id: stat.paper_id,
                        paper_name: stat.paper_name,
                        exam_type: stat.exam_type,
                        exam_count: stat.exam_count,
                        completed_count: stat.completed_count,
                        best_score: stat.best_score,
                        latest_score: stat.latest_score,
                        average_score: stat.average_score,
                        last_exam_detail: stat.last_exam_detail.map(|detail| {
                            LastExamDetailResponse {
                                session_id: detail.session_id,
                                total_score: detail.total_score,
                                reading_score: detail.reading_score,
                                math_score: detail.math_score,
                                accuracy_rate: detail.accuracy_rate,
                                completed_at: detail.completed_at,
                                duration_minutes: detail.duration_minutes,
                                exam_progress: detail.exam_progress,
                                exam_status: detail.exam_status,
                            }
                        }),
                        progress_trend: ProgressTrendResponse {
                            is_improving: stat.progress_trend.is_improving,
                            score_change: stat.progress_trend.score_change,
                            trend_description: stat.progress_trend.trend_description,
                        },
                    }
                }).collect(),
                pagination: PaginationResponse {
                    current_page: response.pagination.page as u32,
                    page_size: response.pagination.per_page as u32,
                    total_count: response.pagination.total,
                    total_pages: response.pagination.total_pages as u32,
                },
            };

            Ok(response::success(http_response))
        }
        Err(e) => {
            error!("考试记录查询失败: user_id={}, error={}", params.user_id, e);
            Ok(response::error_empty(
                ErrorCode::DatabaseError,
                Some(format!("查询考试记录失败: {}", e)),
            ))
        }
    }
}

/// 重新计算用户统计信息
///
/// 当数据不一致时，可以调用此接口重新计算用户的所有考试统计信息
pub async fn recalculate_user_statistics(
    params: web::Query<RecalculateQuery>,
    storage: web::Data<Arc<StorageManager>>,
) -> ActixResult<HttpResponse> {
    info!("重新计算用户统计信息: user_id={}", params.user_id);

    // 创建考试记录服务
    let exam_record_service = ExamRecordServiceImpl::new(storage.get_ref().clone());

    // 调用应用服务
    match exam_record_service.recalculate_user_statistics(params.user_id).await {
        Ok(_) => {
            info!("用户统计信息重新计算成功: user_id={}", params.user_id);
            Ok(response::success("统计信息重新计算完成"))
        }
        Err(e) => {
            error!("用户统计信息重新计算失败: user_id={}, error={}", params.user_id, e);
            Ok(response::error_empty(
                ErrorCode::DatabaseError,
                Some(format!("重新计算统计信息失败: {}", e)),
            ))
        }
    }
}

/// 重新计算查询参数
#[derive(Debug, Deserialize)]
pub struct RecalculateQuery {
    /// 用户ID
    pub user_id: i64,
}

/// 获取指定试卷考试记录
pub async fn get_paper_exam_records(
    request: web::Json<PaperRecordsRequest>,
    storage: web::Data<Arc<StorageManager>>,
) -> ActixResult<HttpResponse> {
    // 验证试卷ID列表不为空
    if request.paper_ids.is_empty() {
        error!("试卷ID列表为空: user_id={}", request.user_id);
        return Ok(response::error_empty(
            ErrorCode::InvalidParameter,
            Some("试卷ID列表不能为空".to_string()),
        ));
    }

    info!("获取指定用户试卷考试记录: user_id={}, paper_ids={:?}", request.user_id, request.paper_ids);

    // 创建考试记录服务
    let exam_record_service = ExamRecordServiceImpl::new(storage.get_ref().clone());

    // 构建请求DTO
    let dto_request = GetPaperExamRecordsRequestDto {
        user_id: request.user_id,
        paper_ids: request.paper_ids.clone(),
    };

    // 调用应用服务
    match exam_record_service.get_paper_exam_records(dto_request).await {
        Ok(result) => {
            info!("指定用户试卷考试记录查询成功: user_id={}, paper_ids={:?}, 记录数={}",
                request.user_id, request.paper_ids, result.paper_statistics.len());

            // 转换为响应格式
            let response = PaperExamRecordsResponse {
                paper_statistics: result.paper_statistics.into_iter().map(|stat| {
                    PaperStatisticsResponse {
                        paper_id: stat.paper_id,
                        paper_name: stat.paper_name,
                        exam_type: stat.exam_type,
                        exam_count: stat.exam_count,
                        completed_count: stat.completed_count,
                        best_score: stat.best_score,
                        latest_score: stat.latest_score,
                        average_score: stat.average_score,
                        last_exam_detail: stat.last_exam_detail.map(|detail| LastExamDetailResponse {
                            session_id: detail.session_id,
                            total_score: detail.total_score,
                            reading_score: detail.reading_score,
                            math_score: detail.math_score,
                            accuracy_rate: detail.accuracy_rate,
                            completed_at: detail.completed_at,
                            duration_minutes: detail.duration_minutes,
                            exam_progress: detail.exam_progress,
                            exam_status: detail.exam_status,
                        }),
                        progress_trend: ProgressTrendResponse {
                            is_improving: stat.progress_trend.is_improving,
                            score_change: stat.progress_trend.score_change,
                            trend_description: stat.progress_trend.trend_description,
                        },
                    }
                }).collect(),
            };

            Ok(response::success(response))
        }
        Err(e) => {
            error!("指定用户试卷考试记录查询失败: user_id={}, paper_ids={:?}, error={}",
                request.user_id, request.paper_ids, e);
            Ok(response::error_empty(
                ErrorCode::DatabaseError,
                Some(format!("查询指定用户试卷考试记录失败: {}", e)),
            ))
        }
    }
}

/// 指定试卷记录请求体
#[derive(Debug, Deserialize)]
pub struct PaperRecordsRequest {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID列表
    pub paper_ids: Vec<i64>,
}

/// 指定试卷考试记录响应
#[derive(Debug, Serialize)]
pub struct PaperExamRecordsResponse {
    /// 考试记录统计列表
    pub paper_statistics: Vec<PaperStatisticsResponse>,
}
