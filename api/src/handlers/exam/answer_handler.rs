//! 考试答题处理器
//!
//! 处理考试答题相关的HTTP请求

use std::sync::Arc;
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::{Deserialize, Serialize};
use tracing::{info, error};

use recommendation_core::application::exam::{
    ExamApplicationService,
    SubmitExamRequestDto,
};
use recommendation_core::models::ErrorCode;
use crate::http::response;
use recommendation_core::domain::exam::value_objects::ModuleType;





/// 获取答题历史请求参数
#[derive(Debug, Deserialize)]
pub struct GetAnswerHistoryQuery {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 模块类型（可选）
    pub module_type: Option<ModuleType>,
}

/// 答题记录
#[derive(Debug, Serialize)]
pub struct AnswerRecord {
    /// 题目ID
    pub question_id: i32,
    /// 学生答案
    pub student_answer: Option<String>,
    /// 是否正确
    pub is_correct: Option<bool>,
    /// 答题用时（秒）
    pub time_spent_seconds: Option<i32>,
    /// 答题时间
    pub answered_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 模块类型
    pub module_type: ModuleType,
    /// 题目序号
    pub question_sequence: i32,
}

/// 获取答题历史响应
#[derive(Debug, Serialize)]
pub struct GetAnswerHistoryResponse {
    /// 会话ID
    pub session_id: String,
    /// 答题记录列表
    pub answers: Vec<AnswerRecord>,
    /// 总答题数
    pub total_answered: i32,
    /// 正确答题数
    pub total_correct: i32,
}

/// 获取答题历史
///
/// GET /api/v1/exam/answer/history?session_id={session_id}&user_id={user_id}
pub async fn get_answer_history(
    query: web::Query<GetAnswerHistoryQuery>,
    _exam_service: web::Data<dyn ExamApplicationService>,
) -> ActixResult<HttpResponse> {
    info!("收到获取答题历史请求: 会话ID={}, 用户ID={}", 
          query.session_id, query.user_id);

    // TODO: 实现答题历史查询逻辑
    // 这里需要：
    // 1. 验证会话权限
    // 2. 查询答题记录
    // 3. 统计答题情况
    // 4. 返回历史数据

    // 暂时返回模拟响应
    let response = GetAnswerHistoryResponse {
        session_id: query.session_id.clone(),
        answers: vec![], // TODO: 实际查询数据
        total_answered: 0,
        total_correct: 0,
    };

    info!("答题历史查询成功: 会话ID={}, 记录数={}",
          query.session_id, response.answers.len());
    Ok(response::success(response))
}



/// 提交考试请求参数
#[derive(Debug, Deserialize)]
pub struct SubmitExamRequest {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 是否强制提交（即使未完成所有题目）
    pub force_submit: Option<bool>,
}

/// 提交整个考试
///
/// POST /api/v1/exam/submit
pub async fn submit_exam(
    request: web::Json<SubmitExamRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到考试提交请求: 会话ID={}, 用户ID={}, 强制提交={:?}",
          request.session_id, request.user_id, request.force_submit);

    // 构建应用服务请求
    let app_request = SubmitExamRequestDto {
        session_id: request.session_id.clone(),
        user_id: request.user_id,
        force_submit: request.force_submit,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.submit_exam(app_request).await {
        Ok(response) => {
            info!("考试提交成功: 会话ID={}, 总分={}/{}, 正确率={:.1}%",
                  request.session_id,
                  response.exam_score.total_score,
                  response.exam_score.max_score,
                  response.statistics.accuracy_rate * 100.0);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("考试提交失败: 会话ID={}, 错误: {}", request.session_id, e);
            Ok(response::error_with_data(
                ErrorCode::InternalError,
                Some(format!("考试提交失败: {}", e)),
                serde_json::json!({}),
            ))
        }
    }
}
