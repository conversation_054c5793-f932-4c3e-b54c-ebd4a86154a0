//! 考试模块提交处理器
//!
//! 处理考试模块提交相关的HTTP请求

use std::sync::Arc;
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::Deserialize;
use tracing::{info, error};

use recommendation_core::application::exam::{
    ExamApplicationService,
    SubmitModuleRequestDto,
};
use recommendation_core::domain::exam::value_objects::ModuleType;
use recommendation_core::models::ErrorCode;
use crate::http::response;

/// 提交模块请求参数
#[derive(Debug, Deserialize)]
pub struct SubmitModuleRequest {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 模块类型
    pub module_type: ModuleType,
    /// 是否强制提交（即使未完成所有题目）
    pub force_submit: Option<bool>,
}

/// 提交模块
///
/// POST /api/v1/exam/module/submit
pub async fn submit_module(
    request: web::Json<SubmitModuleRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到提交模块请求: 会话ID={}, 模块类型={}, 用户ID={}, 强制提交={:?}", 
          request.session_id, request.module_type, request.user_id, request.force_submit);

    // 构建应用服务请求
    let app_request = SubmitModuleRequestDto {
        session_id: request.session_id.clone(),
        user_id: request.user_id,
        module_type: request.module_type,
        force_submit: request.force_submit,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.submit_module(app_request).await {
        Ok(response) => {
            info!("模块提交成功: 会话ID={}, 模块类型={}, 原始分数={}/{}, 正确率={:.1}%, 下一步操作={:?}",
                  request.session_id,
                  request.module_type,
                  response.module_score.raw_score,
                  response.module_score.max_score,
                  response.module_score.accuracy_rate * 100.0,
                  response.next_action.action_type);

            Ok(response::success(response))
        }
        Err(e) => {
            error!("模块提交失败: 会话ID={}, 模块类型={}, 错误: {:?}",
                   request.session_id, request.module_type, e);

            // 根据错误类型返回不同的错误代码
            let error_code = match &e {
                recommendation_core::error::Error::InvalidInput(_) => ErrorCode::ValidationFailed,
                recommendation_core::error::Error::NotFound(_) => ErrorCode::NotFound,
                recommendation_core::error::Error::Service(_) => ErrorCode::BusinessError,
                recommendation_core::error::Error::Database(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::InternalError,
            };

            Ok(response::error_empty(
                error_code,
                Some(format!("模块提交失败: {}", e)),
            ))
        }
    }
}


