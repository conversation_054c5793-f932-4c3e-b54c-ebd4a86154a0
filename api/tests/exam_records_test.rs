mod common;

use common::{TestData, create_test_client, get_base_url, validate_standard_response};
use serde_json::json;

/// 测试获取指定试卷考试记录API (POST方法)
#[tokio::test]
async fn test_get_paper_exam_records() {
    let client = create_test_client();
    let user_id = TestData::user_id();
    let url = format!("{}/exam/records/paper", get_base_url());

    // 测试数据：使用一些试卷ID
    let test_data = json!({
        "user_id": user_id,
        "paper_ids": [700540150387707904i64, 2, 3]
    });

    let response = client
        .post(&url)
        .json(&test_data)
        .send()
        .await
        .expect("请求失败: POST /exam/records/paper");

    // 验证响应状态码
    if response.status().is_success() {
        let json = validate_standard_response(response).await;

        // 验证数据结构
        let data = &json["data"];
        assert!(data.is_object(), "考试记录数据应为对象");
        assert!(data.get("paper_statistics").is_some(), "考试记录缺少paper_statistics字段");
        
        // 验证paper_statistics是数组
        let paper_statistics = data.get("paper_statistics").unwrap();
        assert!(paper_statistics.is_array(), "paper_statistics应为数组");

        println!("✅ 获取指定试卷考试记录测试成功");
        println!("   返回的试卷统计数量: {}", paper_statistics.as_array().unwrap().len());
    } else {
        println!("⚠️  获取指定试卷考试记录接口返回非成功状态码: {}", response.status());
        // 对于测试环境，这可能是正常的（如果没有数据）
    }
}

/// 测试空试卷ID列表的错误处理
#[tokio::test]
async fn test_get_paper_exam_records_empty_list() {
    let client = create_test_client();
    let user_id = TestData::user_id();
    let url = format!("{}/exam/records/paper", get_base_url());

    // 测试数据：空的试卷ID列表
    let test_data = json!({
        "user_id": user_id,
        "paper_ids": []
    });

    let response = client
        .post(&url)
        .json(&test_data)
        .send()
        .await
        .expect("请求失败: POST /exam/records/paper");

    // 应该返回错误状态码
    assert!(!response.status().is_success(), "空试卷ID列表应该返回错误");
    
    let json: serde_json::Value = response.json().await.expect("解析JSON失败");
    
    // 验证错误响应结构
    assert!(json.get("code").is_some(), "错误响应缺少code字段");
    assert!(json.get("msg").is_some(), "错误响应缺少msg字段");
    
    let code = json.get("code").unwrap().as_i64().unwrap();
    assert_ne!(code, 0, "错误响应的code应该不为0");

    println!("✅ 空试卷ID列表错误处理测试成功");
}

/// 测试无效的请求体格式
#[tokio::test]
async fn test_get_paper_exam_records_invalid_format() {
    let client = create_test_client();
    let url = format!("{}/exam/records/paper", get_base_url());

    // 测试数据：缺少必需字段
    let test_data = json!({
        "user_id": 1
        // 缺少paper_ids字段
    });

    let response = client
        .post(&url)
        .json(&test_data)
        .send()
        .await
        .expect("请求失败: POST /exam/records/paper");

    // 应该返回错误状态码
    assert!(!response.status().is_success(), "缺少必需字段应该返回错误");

    println!("✅ 无效请求体格式错误处理测试成功");
}

/// 测试获取考试记录API (GET方法，用于对比)
#[tokio::test]
async fn test_get_exam_records() {
    let client = create_test_client();
    let user_id = TestData::user_id();
    let url = format!("{}/exam/records?user_id={}&page=1&page_size=10", get_base_url(), user_id);

    let response = client
        .get(&url)
        .send()
        .await
        .expect("请求失败: GET /exam/records");

    // 验证响应状态码
    if response.status().is_success() {
        let json = validate_standard_response(response).await;

        // 验证数据结构
        let data = &json["data"];
        assert!(data.is_object(), "考试记录数据应为对象");
        assert!(data.get("paper_statistics").is_some(), "考试记录缺少paper_statistics字段");
        assert!(data.get("pagination").is_some(), "考试记录缺少pagination字段");

        println!("✅ 获取考试记录测试成功");
    } else {
        println!("⚠️  获取考试记录接口返回非成功状态码: {}", response.status());
        // 对于测试环境，这可能是正常的（如果没有数据）
    }
}
