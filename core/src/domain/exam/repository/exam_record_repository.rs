//! 考试记录仓储接口
//!
//! 定义考试记录的数据访问接口

use async_trait::async_trait;
use crate::error::Result;
use super::super::entities::exam_record::{
    ExamRecord, ExamRecordQuery, ExamRecordList, ExamType, ExamStatistics, ExamDetail, ProgressTrend
};

/// 考试记录仓储接口
#[async_trait]
pub trait ExamRecordRepository: Send + Sync {
    /// 根据查询条件获取考试记录列表
    async fn find_exam_records(&self, query: ExamRecordQuery) -> Result<ExamRecordList>;
    
    /// 根据用户ID和试卷ID获取单个考试记录
    async fn find_exam_record(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<Option<ExamRecord>>;
    
    /// 获取用户的所有考试记录（不分页）
    async fn find_all_user_records(&self, user_id: i64) -> Result<Vec<ExamRecord>>;
    
    /// 更新考试统计信息
    async fn update_exam_statistics(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<()>;
    
    /// 获取用户总体统计信息
    async fn get_user_overall_statistics(&self, user_id: i64) -> Result<UserOverallStatistics>;
    
    /// 获取试卷的整体统计信息（所有用户）
    async fn get_paper_statistics(&self, paper_id: i64) -> Result<PaperStatistics>;
    
    /// 检查用户是否有考试记录
    async fn has_exam_records(&self, user_id: i64) -> Result<bool>;
}

/// 用户总体统计信息
#[derive(Debug, Clone)]
pub struct UserOverallStatistics {
    /// 总试卷数
    pub total_papers: u32,
    /// 总考试次数
    pub total_exams: u32,
    /// 总完成次数
    pub total_completed: u32,
    /// 总体平均分
    pub overall_average: Option<f64>,
    /// 最高分
    pub highest_score: Option<u32>,
    /// 最近考试时间
    pub latest_exam_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// 试卷统计信息
#[derive(Debug, Clone)]
pub struct PaperStatistics {
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 参与用户数
    pub participant_count: u32,
    /// 总考试次数
    pub total_attempts: u32,
    /// 平均分
    pub average_score: Option<f64>,
    /// 最高分
    pub highest_score: Option<u32>,
    /// 最低分
    pub lowest_score: Option<u32>,
    /// 完成率
    pub completion_rate: f64,
}

/// 考试记录统计服务接口
/// 
/// 提供考试记录的统计和分析功能
#[async_trait]
pub trait ExamRecordStatisticsService: Send + Sync {
    /// 计算用户在特定试卷上的统计信息
    async fn calculate_exam_statistics(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<ExamStatistics>;
    
    /// 计算进步趋势
    async fn calculate_progress_trend(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<ProgressTrend>;
    
    /// 获取最近考试详情
    async fn get_latest_exam_detail(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<Option<ExamDetail>>;
    
    /// 重新计算并更新所有统计信息
    async fn recalculate_all_statistics(&self, user_id: i64) -> Result<()>;
    
    /// 当考试完成时更新统计信息
    async fn on_exam_completed(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
        session_id: String,
    ) -> Result<()>;
}

/// 考试记录查询构建器
pub struct ExamRecordQueryBuilder {
    user_id: i64,
    paper_id: Option<i64>,
    exam_type: Option<ExamType>,
    page: u32,
    page_size: u32,
}

impl ExamRecordQueryBuilder {
    /// 创建新的查询构建器
    pub fn new(user_id: i64) -> Self {
        Self {
            user_id,
            paper_id: None,
            exam_type: None,
            page: 1,
            page_size: 10,
        }
    }
    
    /// 设置试卷ID过滤
    pub fn with_paper_id(mut self, paper_id: i64) -> Self {
        self.paper_id = Some(paper_id);
        self
    }
    
    /// 设置考试类型过滤
    pub fn with_exam_type(mut self, exam_type: ExamType) -> Self {
        self.exam_type = Some(exam_type);
        self
    }
    
    /// 设置分页参数
    pub fn with_pagination(mut self, page: u32, page_size: u32) -> Self {
        self.page = page;
        self.page_size = page_size;
        self
    }
    
    /// 构建查询条件
    pub fn build(self) -> ExamRecordQuery {
        ExamRecordQuery {
            user_id: self.user_id,
            paper_id: self.paper_id,
            exam_type: self.exam_type,
            pagination: super::super::entities::exam_record::PaginationParams {
                page: self.page,
                page_size: self.page_size,
            },
        }
    }
}

/// 考试记录领域服务
/// 
/// 提供考试记录相关的业务逻辑
pub struct ExamRecordDomainService<R>
where
    R: ExamRecordRepository,
{
    repository: R,
}

impl<R> ExamRecordDomainService<R>
where
    R: ExamRecordRepository,
{
    /// 创建新的领域服务
    pub fn new(repository: R) -> Self {
        Self { repository }
    }
    
    /// 获取用户的考试记录摘要
    pub async fn get_user_exam_summary(&self, user_id: i64) -> Result<UserExamSummary> {
        let overall_stats = self.repository.get_user_overall_statistics(user_id).await?;
        let recent_records = self.repository
            .find_exam_records(
                ExamRecordQueryBuilder::new(user_id)
                    .with_pagination(1, 5)
                    .build()
            )
            .await?;
        
        Ok(UserExamSummary {
            overall_statistics: overall_stats,
            recent_records: recent_records.records,
        })
    }
    
    /// 比较两次考试的表现
    pub async fn compare_exam_performance(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<ExamPerformanceComparison> {
        let record = self.repository
            .find_exam_record(user_id, paper_id, exam_type)
            .await?;
        
        match record {
            Some(record) => {
                let trend = &record.progress_trend;
                Ok(ExamPerformanceComparison {
                    has_previous_exam: trend.score_change != 0,
                    score_change: trend.score_change,
                    is_improving: trend.is_improving,
                    trend_description: trend.trend_description.clone(),
                })
            }
            None => Ok(ExamPerformanceComparison {
                has_previous_exam: false,
                score_change: 0,
                is_improving: false,
                trend_description: "首次考试".to_string(),
            }),
        }
    }
}

/// 用户考试摘要
#[derive(Debug, Clone)]
pub struct UserExamSummary {
    /// 总体统计信息
    pub overall_statistics: UserOverallStatistics,
    /// 最近的考试记录
    pub recent_records: Vec<ExamRecord>,
}

/// 考试表现比较结果
#[derive(Debug, Clone)]
pub struct ExamPerformanceComparison {
    /// 是否有之前的考试记录
    pub has_previous_exam: bool,
    /// 分数变化
    pub score_change: i32,
    /// 是否在进步
    pub is_improving: bool,
    /// 趋势描述
    pub trend_description: String,
}
