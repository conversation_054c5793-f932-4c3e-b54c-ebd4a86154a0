//! 考试记录仓储接口
//!
//! 定义考试记录的数据访问接口

use async_trait::async_trait;
use crate::error::Result;
use super::super::entities::exam_record::{
    ExamRecord, ExamRecordQuery, ExamRecordList, ExamType, ExamStatistics, ExamDetail, ProgressTrend
};

/// 考试记录仓储接口
#[async_trait]
pub trait ExamRecordRepository: Send + Sync {
    /// 根据查询条件获取考试记录列表
    async fn find_exam_records(&self, query: ExamRecordQuery) -> Result<ExamRecordList>;
    
    /// 根据用户ID和试卷ID获取单个考试记录
    async fn find_exam_record(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<Option<ExamRecord>>;
    
    /// 获取用户的所有考试记录（不分页）
    async fn find_all_user_records(&self, user_id: i64) -> Result<Vec<ExamRecord>>;
    
    /// 更新考试统计信息
    async fn update_exam_statistics(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<()>;
    
    /// 获取用户总体统计信息
    async fn get_user_overall_statistics(&self, user_id: i64) -> Result<UserOverallStatistics>;
    
    /// 获取试卷的整体统计信息（所有用户）
    async fn get_paper_statistics(&self, paper_id: i64) -> Result<PaperStatistics>;
    
    /// 检查用户是否有考试记录
    async fn has_exam_records(&self, user_id: i64) -> Result<bool>;

    // === 三层历史记录接口相关方法 ===

    /// 第一层：获取用户历史答卷记录列表（分页）
    async fn get_user_exam_history_list(
        &self,
        user_id: i64,
        page: u64,
        page_size: u64,
    ) -> Result<UserExamHistoryListResult>;

    /// 第二层：获取试卷题目概览数据
    async fn get_exam_questions_overview(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<Option<ExamQuestionsOverviewResult>>;

    /// 第三层：获取模块详细题目数据
    async fn get_module_detailed_questions(
        &self,
        session_id: &str,
        user_id: i64,
        module_type: super::super::value_objects::ModuleType,
    ) -> Result<Option<ModuleDetailedQuestionsResult>>;
}

/// 用户总体统计信息
#[derive(Debug, Clone)]
pub struct UserOverallStatistics {
    /// 总试卷数
    pub total_papers: u32,
    /// 总考试次数
    pub total_exams: u32,
    /// 总完成次数
    pub total_completed: u32,
    /// 总体平均分
    pub overall_average: Option<f64>,
    /// 最高分
    pub highest_score: Option<u32>,
    /// 最近考试时间
    pub latest_exam_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// 试卷统计信息
#[derive(Debug, Clone)]
pub struct PaperStatistics {
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 参与用户数
    pub participant_count: u32,
    /// 总考试次数
    pub total_attempts: u32,
    /// 平均分
    pub average_score: Option<f64>,
    /// 最高分
    pub highest_score: Option<u32>,
    /// 最低分
    pub lowest_score: Option<u32>,
    /// 完成率
    pub completion_rate: f64,
}

/// 考试记录统计服务接口
/// 
/// 提供考试记录的统计和分析功能
#[async_trait]
pub trait ExamRecordStatisticsService: Send + Sync {
    /// 计算用户在特定试卷上的统计信息
    async fn calculate_exam_statistics(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<ExamStatistics>;
    
    /// 计算进步趋势
    async fn calculate_progress_trend(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<ProgressTrend>;
    
    /// 获取最近考试详情
    async fn get_latest_exam_detail(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<Option<ExamDetail>>;
    
    /// 重新计算并更新所有统计信息
    async fn recalculate_all_statistics(&self, user_id: i64) -> Result<()>;
    
    /// 当考试完成时更新统计信息
    async fn on_exam_completed(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
        session_id: String,
    ) -> Result<()>;
}

/// 考试记录查询构建器
pub struct ExamRecordQueryBuilder {
    user_id: i64,
    paper_id: Option<i64>,
    exam_type: Option<ExamType>,
    page: u32,
    page_size: u32,
}

impl ExamRecordQueryBuilder {
    /// 创建新的查询构建器
    pub fn new(user_id: i64) -> Self {
        Self {
            user_id,
            paper_id: None,
            exam_type: None,
            page: 1,
            page_size: 10,
        }
    }
    
    /// 设置试卷ID过滤
    pub fn with_paper_id(mut self, paper_id: i64) -> Self {
        self.paper_id = Some(paper_id);
        self
    }
    
    /// 设置考试类型过滤
    pub fn with_exam_type(mut self, exam_type: ExamType) -> Self {
        self.exam_type = Some(exam_type);
        self
    }
    
    /// 设置分页参数
    pub fn with_pagination(mut self, page: u32, page_size: u32) -> Self {
        self.page = page;
        self.page_size = page_size;
        self
    }
    
    /// 构建查询条件
    pub fn build(self) -> ExamRecordQuery {
        ExamRecordQuery {
            user_id: self.user_id,
            paper_id: self.paper_id,
            exam_type: self.exam_type,
            pagination: super::super::entities::exam_record::PaginationParams {
                page: self.page,
                page_size: self.page_size,
            },
        }
    }
}

/// 考试记录领域服务
/// 
/// 提供考试记录相关的业务逻辑
pub struct ExamRecordDomainService<R>
where
    R: ExamRecordRepository,
{
    repository: R,
}

impl<R> ExamRecordDomainService<R>
where
    R: ExamRecordRepository,
{
    /// 创建新的领域服务
    pub fn new(repository: R) -> Self {
        Self { repository }
    }
    
    /// 获取用户的考试记录摘要
    pub async fn get_user_exam_summary(&self, user_id: i64) -> Result<UserExamSummary> {
        let overall_stats = self.repository.get_user_overall_statistics(user_id).await?;
        let recent_records = self.repository
            .find_exam_records(
                ExamRecordQueryBuilder::new(user_id)
                    .with_pagination(1, 5)
                    .build()
            )
            .await?;
        
        Ok(UserExamSummary {
            overall_statistics: overall_stats,
            recent_records: recent_records.records,
        })
    }
    
    /// 比较两次考试的表现
    pub async fn compare_exam_performance(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<ExamPerformanceComparison> {
        let record = self.repository
            .find_exam_record(user_id, paper_id, exam_type)
            .await?;
        
        match record {
            Some(record) => {
                let trend = &record.progress_trend;
                Ok(ExamPerformanceComparison {
                    has_previous_exam: trend.score_change != 0,
                    score_change: trend.score_change,
                    is_improving: trend.is_improving,
                    trend_description: trend.trend_description.clone(),
                })
            }
            None => Ok(ExamPerformanceComparison {
                has_previous_exam: false,
                score_change: 0,
                is_improving: false,
                trend_description: "首次考试".to_string(),
            }),
        }
    }
}

/// 用户考试摘要
#[derive(Debug, Clone)]
pub struct UserExamSummary {
    /// 总体统计信息
    pub overall_statistics: UserOverallStatistics,
    /// 最近的考试记录
    pub recent_records: Vec<ExamRecord>,
}

/// 考试表现比较结果
#[derive(Debug, Clone)]
pub struct ExamPerformanceComparison {
    /// 是否有之前的考试记录
    pub has_previous_exam: bool,
    /// 分数变化
    pub score_change: i32,
    /// 是否在进步
    pub is_improving: bool,
    /// 趋势描述
    pub trend_description: String,
}

// === 三层历史记录接口数据结构 ===

/// 第一层：用户历史记录列表结果
#[derive(Debug, Clone)]
pub struct UserExamHistoryListResult {
    /// 历史记录列表
    pub exam_histories: Vec<ExamHistoryItemResult>,
    /// 总记录数
    pub total_count: u64,
    /// 当前页码
    pub current_page: u64,
    /// 每页大小
    pub page_size: u64,
}

/// 历史记录项结果（列表项）
#[derive(Debug, Clone)]
pub struct ExamHistoryItemResult {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 考试状态
    pub exam_status: String,
    /// 考试总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
    /// 总题数
    pub total_questions: i32,
    /// 答对题数
    pub correct_count: i32,
    /// 答错题数
    pub incorrect_count: i32,
    /// 未答题数
    pub unanswered_count: i32,
}

/// 第二层：试卷题目概览结果
#[derive(Debug, Clone)]
pub struct ExamQuestionsOverviewResult {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 考试基本信息
    pub exam_info: ExamBasicInfoResult,
    /// 按模块分组的题目状态
    pub modules: Vec<ModuleQuestionsOverviewResult>,
}

/// 考试基本信息结果
#[derive(Debug, Clone)]
pub struct ExamBasicInfoResult {
    /// 考试状态
    pub exam_status: String,
    /// 考试总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
}

/// 模块题目概览结果
#[derive(Debug, Clone)]
pub struct ModuleQuestionsOverviewResult {
    /// 模块类型
    pub module_type: super::super::value_objects::ModuleType,
    /// 学科
    pub subject: super::super::value_objects::Subject,
    /// 模块分数
    pub module_score: Option<u32>,
    /// 模块正确率
    pub module_accuracy: Option<f64>,
    /// 该模块的题目状态列表（不包含题目内容）
    pub questions: Vec<QuestionStatusOverviewResult>,
}

/// 题目状态概览结果（不包含题目内容）
#[derive(Debug, Clone)]
pub struct QuestionStatusOverviewResult {
    /// 题目ID
    pub question_id: i32,
    /// 题目在模块中的序号（1-22或1-27）
    pub module_sequence: i32,
    /// 答题状态：correct=答对, incorrect=答错, unanswered=未答, skipped=跳过
    pub answer_status: String,
    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,
}

/// 第三层：模块详细题目结果
#[derive(Debug, Clone)]
pub struct ModuleDetailedQuestionsResult {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 模块类型
    pub module_type: super::super::value_objects::ModuleType,
    /// 学科
    pub subject: super::super::value_objects::Subject,
    /// 模块基本信息
    pub module_info: ModuleBasicInfoResult,
    /// 该模块的详细题目列表（包含题目内容）
    pub questions: Vec<DetailedQuestionResult>,
}

/// 模块基本信息结果
#[derive(Debug, Clone)]
pub struct ModuleBasicInfoResult {
    /// 模块分数
    pub module_score: Option<u32>,
    /// 模块正确率
    pub module_accuracy: Option<f64>,
    /// 模块用时（分钟）
    pub module_duration: Option<u32>,
    /// 模块状态
    pub module_status: String,
    /// 总题数
    pub total_questions: i32,
    /// 答对题数
    pub correct_count: i32,
    /// 答错题数
    pub incorrect_count: i32,
    /// 未答题数
    pub unanswered_count: i32,
}

/// 详细题目结果（包含完整题目内容）
#[derive(Debug, Clone)]
pub struct DetailedQuestionResult {
    /// 题目ID
    pub question_id: i32,
    /// 题目在模块中的序号（1-22或1-27）
    pub module_sequence: i32,
    /// 题目内容
    pub question_content: String,
    /// 选项列表
    pub options: Vec<String>,
    /// 正确答案
    pub correct_answer: String,
    /// 用户答案
    pub user_answer: Option<String>,
    /// 答题状态：correct=答对, incorrect=答错, unanswered=未答, skipped=跳过
    pub answer_status: String,
    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,
    /// 题目难度（ELO评分）
    pub difficulty: Option<f64>,
    /// 知识点ID
    pub knowledge_point_id: Option<i32>,
    /// 知识点名称
    pub knowledge_point_name: Option<String>,
    /// 题目解析（可选）
    pub explanation: Option<String>,
}
