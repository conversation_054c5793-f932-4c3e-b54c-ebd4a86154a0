//! 考试领域服务
//!
//! 包含考试系统的核心业务逻辑

pub mod adaptive_module_service;

use std::sync::Arc;
use tracing::{info, warn};
use uuid::Uuid;

use crate::error::{Error, Result};

use super::entities::{ExamSession, ExamAnswer, ExamModuleProgress};
use super::value_objects::{ExamType, ModuleType, Subject, SessionConfig, ModuleInfo};
use super::repository::{
    ExamSessionRepository, ExamAnswerRepository, ExamModuleProgressRepository,
    SatPaperRepository, SatPaperQuestion,
};

// 重新导出自适应模块服务
pub use adaptive_module_service::*;

/// 考试会话领域服务
/// 
/// 负责考试会话的创建、管理和状态转换
pub struct ExamSessionDomainService {
    session_repository: Arc<dyn ExamSessionRepository>,
    progress_repository: Arc<dyn ExamModuleProgressRepository>,
    paper_repository: Arc<dyn SatPaperRepository>,
}

impl ExamSessionDomainService {
    /// 创建新的考试会话领域服务
    pub fn new(
        session_repository: Arc<dyn ExamSessionRepository>,
        progress_repository: Arc<dyn ExamModuleProgressRepository>,
        paper_repository: Arc<dyn SatPaperRepository>,
    ) -> Self {
        Self {
            session_repository,
            progress_repository,
            paper_repository,
        }
    }

    /// 创建新的考试会话
    /// 
    /// 这是核心的领域服务方法，封装了会话创建的业务逻辑
    pub async fn create_session(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<ExamSession> {
        info!("创建考试会话，用户ID: {}, 试卷ID: {}, 考试类型: {}", user_id, paper_id, exam_type);

        // 1. 检查用户是否有进行中的会话
        if let Some(active_session) = self.session_repository.find_active_by_user_id(user_id).await? {
            warn!("用户 {} 已有进行中的考试会话: {}", user_id, active_session.session_id);
            return Err(Error::Domain(format!(
                "用户已有进行中的考试会话，请先完成或放弃当前会话"
            )));
        }

        // 2. 验证试卷是否存在
        let paper = self.paper_repository.find_paper_by_id(paper_id).await?
            .ok_or_else(|| Error::NotFound(format!("试卷不存在，ID: {}", paper_id)))?;

        info!("找到试卷: {} ({})", paper.paper_name, paper.version);

        // 3. 生成会话ID
        let session_id = self.generate_session_id();

        // 4. 创建会话实体
        let session = ExamSession::new(session_id.clone(), user_id, paper_id, exam_type);

        // 5. 初始化模块进度
        let module_progresses = self.initialize_module_progresses(&session).await?;

        // 6. 保存到数据库
        self.session_repository.create(&session).await?;
        self.progress_repository.batch_create(&module_progresses).await?;

        info!("成功创建考试会话: {}", session_id);
        Ok(session)
    }

    /// 恢复考试会话状态
    /// 
    /// 用于用户中途退出后重新进入时恢复状态
    pub async fn resume_session(&self, user_id: i64) -> Result<Option<ExamSession>> {
        info!("尝试恢复用户 {} 的考试会话", user_id);

        let session = self.session_repository.find_active_by_user_id(user_id).await?;

        if let Some(ref session) = session {
            info!("找到进行中的会话: {}", session.session_id);
        } else {
            info!("用户 {} 没有进行中的考试会话", user_id);
        }

        Ok(session)
    }

    /// 生成会话ID
    fn generate_session_id(&self) -> String {
        let uuid = Uuid::new_v4();
        format!("exam_{}", uuid.simple())
    }

    /// 初始化模块进度（只创建模块1）
    ///
    /// 根据自适应逻辑，初始化时只创建模块1的进度记录
    /// 模块2的记录将在模块1完成后根据表现动态创建
    async fn initialize_module_progresses(
        &self,
        session: &ExamSession,
    ) -> Result<Vec<ExamModuleProgress>> {
        let config = SessionConfig::from_exam_type(session.exam_type);
        let mut progresses = Vec::new();

        for subject in &config.subjects {
            // 只创建模块1的进度记录
            let module1_info = ModuleInfo::new(ModuleType::Module1, *subject);
            let progress = ExamModuleProgress::new(
                session.session_id.clone(),
                session.user_id,
                session.paper_id,
                module1_info.module_type,
                *subject,
                module1_info.question_count,
                module1_info.time_limit_minutes * 60, // 转换为秒
            );
            progresses.push(progress);
        }

        info!("初始化了 {} 个模块1进度记录", progresses.len());
        Ok(progresses)
    }
}

/// 考试题目管理领域服务
/// 
/// 负责题目的筛选、组织和分发
pub struct ExamQuestionDomainService {
    paper_repository: Arc<dyn SatPaperRepository>,
    answer_repository: Arc<dyn ExamAnswerRepository>,
}

impl ExamQuestionDomainService {
    /// 创建新的题目管理领域服务
    pub fn new(
        paper_repository: Arc<dyn SatPaperRepository>,
        answer_repository: Arc<dyn ExamAnswerRepository>,
    ) -> Self {
        Self {
            paper_repository,
            answer_repository,
        }
    }

    /// 获取模块的题目列表
    /// 
    /// 根据考试类型和模块类型筛选题目
    pub async fn get_module_questions(
        &self,
        paper_id: i64,
        exam_type: ExamType,
        module_type: ModuleType,
        subject: Subject,
    ) -> Result<Vec<SatPaperQuestion>> {
        info!("获取模块题目，试卷ID: {}, 考试类型: {}, 模块: {}, 学科: {}", 
              paper_id, exam_type, module_type, subject);

        // 1. 获取试卷的所有题目
        let all_questions = self.paper_repository
            .find_questions_by_paper_and_exam_type(paper_id, exam_type)
            .await?;

        // 2. 按学科和模块类型筛选
        let filtered_questions: Vec<SatPaperQuestion> = all_questions
            .into_iter()
            .filter(|q| {
                // 检查学科匹配
                let question_subject = q.get_subject();
                if question_subject != subject {
                    return false;
                }

                // 检查模块类型匹配
                if let Some(question_module_type) = q.get_module_type() {
                    question_module_type == module_type
                } else {
                    false
                }
            })
            .collect();

        info!("筛选后的题目数量: {}", filtered_questions.len());

        // 3. 按模块内顺序排序
        let mut sorted_questions = filtered_questions;
        sorted_questions.sort_by_key(|q| q.module_sequence);

        Ok(sorted_questions)
    }

    /// 初始化模块的答题记录
    /// 
    /// 为模块中的每道题创建初始的答题记录
    pub async fn initialize_module_answers(
        &self,
        session_id: String,
        user_id: i64,
        paper_id: i64,
        questions: &[SatPaperQuestion],
        module_type: ModuleType,
    ) -> Result<()> {
        info!("初始化模块答题记录，会话ID: {}, 题目数量: {}", session_id, questions.len());

        // 检查是否已经存在答题记录
        let existing_answers = self.answer_repository.find_by_session_and_module(&session_id, module_type).await?;

        if !existing_answers.is_empty() {
            info!("会话 {} 模块 {:?} 的答题记录已存在，跳过初始化", session_id, module_type);
            return Ok(());
        }

        let mut answers = Vec::new();

        for question in questions {
            let answer = ExamAnswer::new(
                session_id.clone(),
                user_id,
                paper_id,
                question.question_id,
                module_type,
                question.module_sequence,
            );
            answers.push(answer);
        }

        self.answer_repository.batch_create(&answers).await?;

        info!("成功初始化 {} 条答题记录", answers.len());
        Ok(())
    }
}

/// 自适应模块选择领域服务
/// 
/// 负责根据第一模块的表现选择第二模块的难度
pub struct AdaptiveModuleSelectionService;

impl AdaptiveModuleSelectionService {
    /// 根据第一模块的表现选择第二模块类型
    /// 
    /// 这是核心的自适应逻辑
    pub fn select_second_module_type(
        &self,
        first_module_correct_rate: f64,
        total_questions: i32,
    ) -> ModuleType {
        info!("选择第二模块类型，第一模块正确率: {:.2}%, 总题数: {}", 
              first_module_correct_rate * 100.0, total_questions);

        // 简单的自适应逻辑：正确率超过60%选择困难版本，否则选择简单版本
        // 实际实现中可能需要更复杂的算法
        let threshold = 0.6;

        let selected_type = if first_module_correct_rate >= threshold {
            ModuleType::Module2H
        } else {
            ModuleType::Module2E
        };

        info!("选择的第二模块类型: {}", selected_type);
        selected_type
    }

    /// 获取自适应信息描述
    pub fn get_adaptive_info(&self, module_type: ModuleType, correct_rate: f64) -> String {
        match module_type {
            ModuleType::Module2H => {
                format!("根据第一模块 {:.1}% 的正确率，系统选择了困难版本的第二模块", correct_rate * 100.0)
            }
            ModuleType::Module2E => {
                format!("根据第一模块 {:.1}% 的正确率，系统选择了简单版本的第二模块", correct_rate * 100.0)
            }
            ModuleType::Module1 => "第一模块，标准难度".to_string(),
        }
    }
}

/// 考试记录领域服务
///
/// 负责考试记录的查询和数据组装业务逻辑
pub struct ExamRecordDomainService {
    session_repository: Arc<dyn ExamSessionRepository>,
    answer_repository: Arc<dyn ExamAnswerRepository>,
    paper_repository: Arc<dyn SatPaperRepository>,
}

impl ExamRecordDomainService {
    /// 创建新的考试记录领域服务
    pub fn new(
        session_repository: Arc<dyn ExamSessionRepository>,
        answer_repository: Arc<dyn ExamAnswerRepository>,
        paper_repository: Arc<dyn SatPaperRepository>,
    ) -> Self {
        Self {
            session_repository,
            answer_repository,
            paper_repository,
        }
    }

    /// 获取考试概览数据
    ///
    /// 验证权限并组装概览所需的数据
    pub async fn get_exam_overview_data(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<ExamOverviewData> {
        info!("获取考试概览数据: session_id={}, user_id={}", session_id, user_id);

        // 1. 验证会话权限
        let session = self.session_repository
            .find_by_session_id(session_id)
            .await?
            .ok_or_else(|| Error::NotFound("考试会话不存在".to_string()))?;

        if session.user_id != user_id {
            return Err(Error::service("无权限访问此考试会话"));
        }

        // 2. 获取试卷信息
        let paper = self.paper_repository
            .find_paper_by_id(session.paper_id)
            .await?
            .ok_or_else(|| Error::NotFound("试卷不存在".to_string()))?;

        // 3. 获取试卷的所有题目（按模块分组）
        let all_questions = self.paper_repository
            .find_questions_by_paper_and_exam_type(session.paper_id, session.exam_type)
            .await?;

        // 4. 获取所有答题记录
        let all_answers = self.answer_repository
            .find_by_session_id(session_id)
            .await?;

        // 5. 组装数据
        Ok(ExamOverviewData {
            session_id: session.session_id,
            paper_id: session.paper_id,
            paper_name: paper.paper_name,
            exam_type: session.exam_type.to_string(),
            questions: all_questions,
            answers: all_answers,
        })
    }

    /// 获取模块详情数据
    ///
    /// 验证权限并组装模块详情所需的完整数据
    pub async fn get_module_details_data(
        &self,
        session_id: &str,
        user_id: i64,
        module_type: ModuleType,
    ) -> Result<ModuleDetailsData> {
        info!("获取模块详情数据: session_id={}, user_id={}, module_type={}",
              session_id, user_id, module_type);

        // 1. 验证会话权限
        let session = self.session_repository
            .find_by_session_id(session_id)
            .await?
            .ok_or_else(|| Error::NotFound("考试会话不存在".to_string()))?;

        if session.user_id != user_id {
            return Err(Error::service("无权限访问此考试会话"));
        }

        // 2. 获取该模块的题目列表
        let all_questions = self.paper_repository
            .find_questions_by_paper_and_exam_type(session.paper_id, session.exam_type)
            .await?;

        // 3. 按模块类型过滤题目
        let module_questions: Vec<SatPaperQuestion> = all_questions
            .into_iter()
            .filter(|q| {
                if let Some(question_module_type) = q.get_module_type() {
                    question_module_type == module_type
                } else {
                    false
                }
            })
            .collect();

        if module_questions.is_empty() {
            return Err(Error::NotFound("指定模块不存在或无题目".to_string()));
        }

        // 4. 获取该模块的答题记录
        let module_answers = self.answer_repository
            .find_by_session_and_module(session_id, module_type)
            .await?;

        // 4. 确定学科（从第一个题目获取）
        let subject = module_questions[0].get_subject();

        Ok(ModuleDetailsData {
            session_id: session.session_id,
            module_type,
            subject,
            questions: module_questions,
            answers: module_answers,
        })
    }
}

/// 考试概览领域数据
///
/// 包含考试概览所需的所有领域数据
#[derive(Debug)]
pub struct ExamOverviewData {
    pub session_id: String,
    pub paper_id: i64,
    pub paper_name: String,
    pub exam_type: String,
    pub questions: Vec<SatPaperQuestion>,
    pub answers: Vec<ExamAnswer>,
}

/// 模块详情领域数据
///
/// 包含模块详情所需的所有领域数据
#[derive(Debug)]
pub struct ModuleDetailsData {
    pub session_id: String,
    pub module_type: ModuleType,
    pub subject: Subject,
    pub questions: Vec<SatPaperQuestion>,
    pub answers: Vec<ExamAnswer>,
}
