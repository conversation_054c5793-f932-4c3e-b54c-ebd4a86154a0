//! 考试记录领域服务
//!
//! 包含考试记录相关的核心业务逻辑

use std::sync::Arc;
use async_trait::async_trait;

use super::super::entities::exam_record::{ExamRecord, ExamStatistics, ProgressTrend};
use super::super::repository::exam_record_repository::ExamRecordRepository;
use super::super::value_objects::{ExamType, ExamStatistics as ExamStatsValue, ProgressTrend as ProgressTrendValue};
use crate::error::{Error, Result};

/// 考试记录领域服务
///
/// 包含考试记录的核心业务逻辑，处理跨实体的业务规则
pub struct ExamRecordDomainService {
    /// 考试记录仓储
    repository: Arc<dyn ExamRecordRepository>,
}

impl ExamRecordDomainService {
    /// 创建考试记录领域服务
    pub fn new(repository: Arc<dyn ExamRecordRepository>) -> Self {
        Self { repository }
    }
    
    /// 计算考试统计信息
    ///
    /// 基于用户的考试历史计算统计数据
    pub async fn calculate_exam_statistics(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<ExamStatistics> {
        // 这里应该从考试历史表中查询数据并计算统计信息
        // 目前先返回默认值，实际实现需要查询数据库
        
        // TODO: 实现统计计算逻辑
        // 1. 查询用户在该试卷的所有考试记录
        // 2. 计算考试次数、完成次数
        // 3. 计算最高分、最近分数、平均分
        
        Ok(ExamStatistics {
            exam_count: 0,
            completed_count: 0,
            best_score: None,
            latest_score: None,
            average_score: None,
        })
    }
    
    /// 计算进步趋势
    ///
    /// 基于最近两次考试成绩计算进步趋势
    pub async fn calculate_progress_trend(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<ProgressTrend> {
        // TODO: 实现趋势计算逻辑
        // 1. 查询最近两次完成的考试记录
        // 2. 比较分数变化
        // 3. 生成趋势描述
        
        Ok(ProgressTrend {
            is_improving: false,
            score_change: 0,
            trend_description: "暂无数据".to_string(),
        })
    }
    
    /// 更新考试记录统计
    ///
    /// 当有新的考试完成时，更新相关的统计信息
    pub async fn update_exam_record_statistics(
        &self,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<()> {
        // 计算新的统计信息
        let statistics = self.calculate_exam_statistics(user_id, paper_id, exam_type).await?;
        let progress_trend = self.calculate_progress_trend(user_id, paper_id, exam_type).await?;
        
        // 更新统计信息
        self.repository.update_exam_statistics(user_id, paper_id, exam_type).await?;
        
        Ok(())
    }
    
    /// 验证考试记录业务规则
    ///
    /// 验证考试记录是否符合业务规则
    pub fn validate_exam_record(&self, exam_record: &ExamRecord) -> Result<()> {
        // 基本验证
        if exam_record.user_id <= 0 {
            return Err(Error::InvalidInput("用户ID必须大于0".to_string()));
        }
        
        if exam_record.paper_id <= 0 {
            return Err(Error::InvalidInput("试卷ID必须大于0".to_string()));
        }
        
        if exam_record.paper_name.trim().is_empty() {
            return Err(Error::InvalidInput("试卷名称不能为空".to_string()));
        }
        
        // 统计信息验证
        if exam_record.statistics.exam_count < 0 {
            return Err(Error::InvalidInput("考试次数不能为负数".to_string()));
        }
        
        if exam_record.statistics.completed_count < 0 {
            return Err(Error::InvalidInput("完成次数不能为负数".to_string()));
        }
        
        if exam_record.statistics.completed_count > exam_record.statistics.exam_count {
            return Err(Error::InvalidInput("完成次数不能大于考试次数".to_string()));
        }
        
        // 分数验证
        if let Some(best_score) = exam_record.statistics.best_score {
            if best_score > 1600 {
                return Err(Error::InvalidInput("最高分不能超过1600".to_string()));
            }
        }
        
        if let Some(latest_score) = exam_record.statistics.latest_score {
            if latest_score > 1600 {
                return Err(Error::InvalidInput("最近分数不能超过1600".to_string()));
            }
        }
        
        if let Some(average_score) = exam_record.statistics.average_score {
            if average_score < 0.0 || average_score > 1600.0 {
                return Err(Error::InvalidInput("平均分必须在0-1600之间".to_string()));
            }
        }
        
        Ok(())
    }
    
    /// 比较两个考试记录的表现
    ///
    /// 用于分析用户在不同试卷或不同时间的表现差异
    pub fn compare_exam_performance(
        &self,
        record1: &ExamRecord,
        record2: &ExamRecord,
    ) -> ExamPerformanceComparison {
        let score_diff = match (record1.statistics.latest_score, record2.statistics.latest_score) {
            (Some(s1), Some(s2)) => s1 as i32 - s2 as i32,
            _ => 0,
        };
        
        let completion_rate_diff = record1.statistics.completion_rate() - record2.statistics.completion_rate();
        
        ExamPerformanceComparison {
            score_difference: score_diff,
            completion_rate_difference: completion_rate_diff,
            is_improving: score_diff > 0 || (score_diff == 0 && completion_rate_diff > 0.0),
        }
    }
    
    /// 检查用户是否为新用户
    ///
    /// 基于考试记录判断用户是否为新用户
    pub async fn is_new_user(&self, user_id: i64) -> Result<bool> {
        self.repository.has_exam_records(user_id).await.map(|has_records| !has_records)
    }
    
    /// 获取用户的考试表现摘要
    ///
    /// 提供用户整体考试表现的摘要信息
    pub async fn get_user_performance_summary(&self, user_id: i64) -> Result<UserPerformanceSummary> {
        let overall_stats = self.repository.get_user_overall_statistics(user_id).await?;

        Ok(UserPerformanceSummary {
            total_papers: overall_stats.total_papers,
            total_exams: overall_stats.total_exams,
            total_completed: overall_stats.total_completed,
            overall_completion_rate: if overall_stats.total_exams > 0 {
                overall_stats.total_completed as f64 / overall_stats.total_exams as f64
            } else {
                0.0
            },
            highest_score: overall_stats.highest_score,
            overall_average: overall_stats.overall_average,
            latest_exam_at: overall_stats.latest_exam_at,
        })
    }

    /// 获取考试历史的所有题目和答题情况
    ///
    /// 根据session_id获取该次考试的所有题目详情和用户答题情况
    pub async fn get_exam_history_questions(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<ExamHistoryData> {
        // TODO: 实现获取考试历史题目的逻辑
        // 1. 验证session_id和user_id的匹配性
        // 2. 获取考试基本信息
        // 3. 获取所有模块的题目和答题情况
        // 4. 组装返回数据

        Err(Error::NotImplemented("获取考试历史题目功能尚未实现".to_string()))
    }

    /// 获取单个模块的详细题目和答题情况
    ///
    /// 根据session_id和module_type获取指定模块的题目详情和答题情况
    pub async fn get_module_detailed_questions(
        &self,
        session_id: &str,
        user_id: i64,
        module_type: super::super::value_objects::ModuleType,
    ) -> Result<ModuleDetailedData> {
        // TODO: 实现获取模块详细题目的逻辑
        // 1. 验证session_id、user_id和module_type的有效性
        // 2. 获取模块基本信息和统计数据
        // 3. 获取该模块的所有题目内容和答题情况
        // 4. 组装返回数据

        Err(Error::NotImplemented("获取模块详细题目功能尚未实现".to_string()))
    }
}

/// 考试表现比较结果
#[derive(Debug, Clone)]
pub struct ExamPerformanceComparison {
    /// 分数差异
    pub score_difference: i32,
    /// 完成率差异
    pub completion_rate_difference: f64,
    /// 是否在进步
    pub is_improving: bool,
}

/// 用户表现摘要
#[derive(Debug, Clone)]
pub struct UserPerformanceSummary {
    /// 总试卷数
    pub total_papers: u32,
    /// 总考试次数
    pub total_exams: u32,
    /// 总完成次数
    pub total_completed: u32,
    /// 整体完成率
    pub overall_completion_rate: f64,
    /// 最高分
    pub highest_score: Option<u32>,
    /// 整体平均分
    pub overall_average: Option<f64>,
    /// 最近考试时间
    pub latest_exam_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// 考试历史数据
#[derive(Debug, Clone)]
pub struct ExamHistoryData {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: super::super::value_objects::ExamType,
    /// 考试状态
    pub exam_status: String,
    /// 考试总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
    /// 模块数据列表
    pub modules: Vec<ExamHistoryModuleData>,
}

/// 考试历史模块数据
#[derive(Debug, Clone)]
pub struct ExamHistoryModuleData {
    /// 模块类型
    pub module_type: super::super::value_objects::ModuleType,
    /// 学科
    pub subject: super::super::value_objects::Subject,
    /// 模块分数
    pub module_score: Option<u32>,
    /// 模块正确率
    pub module_accuracy: Option<f64>,
    /// 模块用时（分钟）
    pub module_duration: Option<u32>,
    /// 题目列表
    pub questions: Vec<ExamHistoryQuestionData>,
}

/// 考试历史题目数据
#[derive(Debug, Clone)]
pub struct ExamHistoryQuestionData {
    /// 题目ID
    pub question_id: i32,
    /// 题目在模块中的序号
    pub module_sequence: i32,
    /// 题目内容
    pub question_content: String,
    /// 选项列表
    pub options: Vec<String>,
    /// 正确答案
    pub correct_answer: String,
    /// 用户答案
    pub user_answer: Option<String>,
    /// 答题状态
    pub answer_status: String,
    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,
    /// 题目难度
    pub difficulty: Option<f64>,
    /// 知识点ID
    pub knowledge_point_id: Option<i32>,
    /// 知识点名称
    pub knowledge_point_name: Option<String>,
}

/// 模块详细数据
#[derive(Debug, Clone)]
pub struct ModuleDetailedData {
    /// 会话ID
    pub session_id: String,
    /// 模块类型
    pub module_type: super::super::value_objects::ModuleType,
    /// 学科
    pub subject: super::super::value_objects::Subject,
    /// 模块分数
    pub module_score: Option<u32>,
    /// 模块正确率
    pub module_accuracy: Option<f64>,
    /// 模块用时（分钟）
    pub module_duration: Option<u32>,
    /// 模块状态
    pub module_status: String,
    /// 题目列表
    pub questions: Vec<ExamHistoryQuestionData>,
}
