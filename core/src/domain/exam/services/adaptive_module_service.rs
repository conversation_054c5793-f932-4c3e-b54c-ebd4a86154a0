//! 自适应模块服务
//!
//! 负责根据模块1的表现确定模块2的类型

use std::sync::Arc;
use tracing::{info, warn};

use crate::error::{Error, Result};
use super::super::entities::{ExamAnswer, ExamModuleProgress};
use super::super::value_objects::{ModuleType, Subject};
use super::super::repository::{ExamAnswerRepository, ExamModuleProgressRepository};

/// 自适应模块服务
/// 
/// 根据SAT考试规则，模块2的类型需要根据模块1的表现来确定
pub struct AdaptiveModuleService {
    answer_repository: Arc<dyn ExamAnswerRepository>,
    progress_repository: Arc<dyn ExamModuleProgressRepository>,
}

impl AdaptiveModuleService {
    /// 创建新的自适应模块服务
    pub fn new(
        answer_repository: Arc<dyn ExamAnswerRepository>,
        progress_repository: Arc<dyn ExamModuleProgressRepository>,
    ) -> Self {
        Self {
            answer_repository,
            progress_repository,
        }
    }

    /// 评估模块1表现并创建模块2进度记录
    ///
    /// # 参数
    /// * `session_id` - 会话ID
    /// * `subject` - 学科
    ///
    /// # 返回
    /// 返回确定的模块2类型（2E或2H）
    pub async fn evaluate_and_create_module2(
        &self,
        session_id: &str,
        subject: Subject,
    ) -> Result<ModuleType> {
        info!("开始评估模块1表现并创建模块2: 会话ID={}, 学科={}", session_id, subject);

        // 1. 获取模块1的进度记录
        let module1_type_str = format!("{}_1", subject.to_string().to_lowercase());
        let module1_progress = self.find_progress_by_module_type(session_id, &module1_type_str).await?;

        if module1_progress.is_none() {
            warn!("未找到模块1的进度记录，默认使用简单模块");
            return self.create_module2_progress(session_id, subject, ModuleType::Module2E).await;
        }

        let module1_progress = module1_progress.unwrap();

        // 2. 计算正确率
        let correct_rate = if module1_progress.answered_questions > 0 {
            module1_progress.correct_questions as f32 / module1_progress.answered_questions as f32
        } else {
            0.0
        };

        info!("模块1正确率: {:.2}% ({}/{})",
              correct_rate * 100.0,
              module1_progress.correct_questions,
              module1_progress.answered_questions);

        // 3. 根据阈值确定模块2类型
        let threshold = self.get_adaptive_threshold(subject);
        let module2_type = if correct_rate >= threshold {
            ModuleType::Module2H
        } else {
            ModuleType::Module2E
        };

        info!("确定模块2类型: {} (阈值: {:.2}%, 实际: {:.2}%)",
              module2_type, threshold, correct_rate * 100.0);

        // 4. 创建模块2的进度记录
        self.create_module2_progress(session_id, subject, module2_type).await
    }

    /// 检查模块2是否已创建
    ///
    /// # 参数
    /// * `session_id` - 会话ID
    /// * `subject` - 学科
    ///
    /// # 返回
    /// 如果已创建返回模块类型，否则返回None
    pub async fn check_module2_exists(
        &self,
        session_id: &str,
        subject: Subject,
    ) -> Result<Option<ModuleType>> {
        info!("检查模块2是否已创建: 会话ID={}, 学科={}", session_id, subject);

        // 检查2E类型
        let module2e_type = format!("{}_2E", subject.to_string().to_lowercase());
        if let Some(_) = self.find_progress_by_module_type(session_id, &module2e_type).await? {
            info!("找到模块2E记录");
            return Ok(Some(ModuleType::Module2E));
        }

        // 检查2H类型
        let module2h_type = format!("{}_2H", subject.to_string().to_lowercase());
        if let Some(_) = self.find_progress_by_module_type(session_id, &module2h_type).await? {
            info!("找到模块2H记录");
            return Ok(Some(ModuleType::Module2H));
        }

        info!("尚未创建模块2记录");
        Ok(None)
    }

    /// 根据模块类型字符串查找进度记录
    async fn find_progress_by_module_type(
        &self,
        session_id: &str,
        module_type_str: &str,
    ) -> Result<Option<ExamModuleProgress>> {
        let progresses = self.progress_repository
            .find_by_session_id(session_id)
            .await?;

        for progress in progresses {
            // 从存储的module_type中提取，格式为 "subject_type"
            if let Some(stored_type) = self.extract_module_type_from_stored(&progress) {
                if stored_type == module_type_str {
                    return Ok(Some(progress));
                }
            }
        }

        Ok(None)
    }

    /// 从存储的进度记录中提取模块类型
    fn extract_module_type_from_stored(&self, progress: &ExamModuleProgress) -> Option<String> {
        // 这里需要根据实际的存储格式来解析
        // 假设存储格式为 "subject_type"，如 "reading_1", "math_2E"
        Some(format!("{}_{}", progress.subject.to_string().to_lowercase(), progress.module_type.to_string()))
    }

    /// 计算表现分数（0-100）
    /// 
    /// 基于正确率计算，可以根据需要扩展为更复杂的评分算法
    fn calculate_performance_score(&self, answers: &[ExamAnswer]) -> f32 {
        if answers.is_empty() {
            return 0.0;
        }

        let correct_count = answers.iter()
            .filter(|a| a.is_correct.unwrap_or(false))
            .count();

        (correct_count as f32 / answers.len() as f32) * 100.0
    }

    /// 获取自适应阈值
    ///
    /// 根据学科返回不同的阈值，这些值可以根据实际数据调整
    pub fn get_adaptive_threshold(&self, subject: Subject) -> f32 {
        match subject {
            Subject::Reading => 60.0,  // 阅读60%正确率进入2H
            Subject::Math => 65.0,     // 数学65%正确率进入2H
        }
    }

    /// 创建模块2进度记录
    ///
    /// 根据确定的模块类型创建进度记录
    async fn create_module2_progress(
        &self,
        session_id: &str,
        subject: Subject,
        module2_type: ModuleType,
    ) -> Result<ModuleType> {
        info!("创建模块2进度记录: 会话ID={}, 学科={}, 类型={}",
              session_id, subject, module2_type);

        // 获取模块1的基本信息用于创建模块2
        let module1_type_str = format!("{}_1", subject.to_string().to_lowercase());
        let module1_progress = self.find_progress_by_module_type(session_id, &module1_type_str).await?
            .ok_or_else(|| Error::NotFound("模块1进度记录不存在".to_string()))?;

        // 创建模块2进度记录
        let module2_progress = ExamModuleProgress::new(
            session_id.to_string(),
            module1_progress.user_id,
            module1_progress.paper_id,
            module2_type,
            subject,
            module2_type.question_count(subject),
            module2_type.time_limit_minutes(subject) * 60,
        );

        self.progress_repository.create(&module2_progress).await?;

        info!("成功创建模块2进度记录: {}", module2_type);
        Ok(module2_type)
    }

    /// 检查模块1是否已完成
    /// 
    /// 用于验证是否可以进行自适应评估
    pub async fn is_module1_completed(
        &self,
        session_id: &str,
        subject: Subject,
    ) -> Result<bool> {
        let progresses = self.progress_repository
            .find_by_session_and_subject(session_id, subject)
            .await?;

        for progress in progresses {
            if progress.module_type == ModuleType::Module1 {
                return Ok(progress.module_status.is_completed());
            }
        }

        Ok(false)
    }

    /// 获取模块1的表现分数
    /// 
    /// 用于前端显示
    pub async fn get_module1_performance_score(
        &self,
        session_id: &str,
        _subject: Subject,
    ) -> Result<Option<f32>> {
        let answers = self.answer_repository
            .find_by_session_and_module(session_id, ModuleType::Module1)
            .await?;

        if answers.is_empty() {
            return Ok(None);
        }

        let score = self.calculate_performance_score(&answers);
        Ok(Some(score))
    }
}

/// 自适应模块配置
/// 
/// 用于配置不同学科的自适应参数
#[derive(Debug, Clone)]
pub struct AdaptiveModuleConfig {
    /// 学科
    pub subject: Subject,
    /// 进入困难模块的阈值（百分比）
    pub threshold_score: f32,
    /// 最小答题数量要求
    pub min_answers_required: i32,
}

impl AdaptiveModuleConfig {
    /// 获取默认配置
    pub fn default_configs() -> Vec<Self> {
        vec![
            Self {
                subject: Subject::Reading,
                threshold_score: 60.0,
                min_answers_required: 20,
            },
            Self {
                subject: Subject::Math,
                threshold_score: 65.0,
                min_answers_required: 18,
            },
        ]
    }

    /// 根据学科获取配置
    pub fn for_subject(subject: Subject) -> Self {
        Self::default_configs()
            .into_iter()
            .find(|config| config.subject == subject)
            .unwrap_or_else(|| Self {
                subject,
                threshold_score: 60.0,
                min_answers_required: 20,
            })
    }
}
