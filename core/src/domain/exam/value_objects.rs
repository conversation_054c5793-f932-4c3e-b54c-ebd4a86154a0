//! 考试领域值对象
//!
//! 定义考试系统的值对象，表示不可变的业务概念

use serde::{Deserialize, Serialize};
use std::fmt;

/// 考试类型
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ExamType {
    /// 全长考试
    Full,
    /// 仅数学
    Math,
    /// 仅语言
    Reading,
}

impl fmt::Display for ExamType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ExamType::Full => write!(f, "full"),
            ExamType::Math => write!(f, "math"),
            ExamType::Reading => write!(f, "reading"),
        }
    }
}

impl ExamType {
    /// 获取考试总题数
    pub fn total_questions(&self) -> i32 {
        match self {
            ExamType::Full => 98,      // 54 + 44
            ExamType::Reading => 54,   // 27 + 27
            ExamType::Math => 44,      // 22 + 22
        }
    }

    /// 获取考试总时长（分钟）
    pub fn total_time_minutes(&self) -> i32 {
        match self {
            ExamType::Full => 134,     // 64 + 70
            ExamType::Reading => 64,   // 32 + 32
            ExamType::Math => 70,      // 35 + 35
        }
    }

    /// 检查是否包含指定学科
    pub fn includes_subject(&self, subject: Subject) -> bool {
        match self {
            ExamType::Full => true,
            ExamType::Reading => subject == Subject::Reading,
            ExamType::Math => subject == Subject::Math,
        }
    }
}

/// 会话状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum SessionStatus {
    /// 进行中
    InProgress,
    /// 已完成
    Completed,
}

impl fmt::Display for SessionStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SessionStatus::InProgress => write!(f, "in_progress"),
            SessionStatus::Completed => write!(f, "completed"),
        }
    }
}

/// 学科
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Subject {
    /// 语言部分
    Reading,
    /// 数学部分
    Math,
}

impl fmt::Display for Subject {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Subject::Reading => write!(f, "reading"),
            Subject::Math => write!(f, "math"),
        }
    }
}

impl Subject {
    /// 获取学科名称
    pub fn name(&self) -> &'static str {
        match self {
            Subject::Reading => "Reading and Writing",
            Subject::Math => "Math",
        }
    }

    /// 获取学科ID（对应数据库中的subject_id）
    pub fn subject_id(&self) -> i32 {
        match self {
            Subject::Reading => 14,
            Subject::Math => 1,
        }
    }
}

impl From<Subject> for String {
    fn from(subject: Subject) -> Self {
        match subject {
            Subject::Reading => "reading".to_string(),
            Subject::Math => "math".to_string(),
        }
    }
}

impl From<String> for Subject {
    fn from(s: String) -> Self {
        match s.as_str() {
            "reading" => Subject::Reading,
            "math" => Subject::Math,
            _ => Subject::Reading, // 默认值
        }
    }
}

/// 模块类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ModuleType {
    /// 第一模块
    #[serde(rename = "1")]
    Module1,
    /// 第二模块简单版
    #[serde(rename = "2E")]
    Module2E,
    /// 第二模块困难版
    #[serde(rename = "2H")]
    Module2H,
}

impl fmt::Display for ModuleType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ModuleType::Module1 => write!(f, "1"),
            ModuleType::Module2E => write!(f, "2E"),
            ModuleType::Module2H => write!(f, "2H"),
        }
    }
}

impl ModuleType {
    /// 从字符串解析模块类型
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "1" => Some(ModuleType::Module1),
            "2E" => Some(ModuleType::Module2E),
            "2H" => Some(ModuleType::Module2H),
            _ => None,
        }
    }

    /// 转换为字符串
    pub fn to_string(&self) -> String {
        format!("{}", self)
    }

    /// 获取模块题数
    pub fn question_count(&self, subject: Subject) -> i32 {
        match subject {
            Subject::Reading => 27,
            Subject::Math => 22,
        }
    }

    /// 获取模块时长（分钟）
    pub fn time_limit_minutes(&self, subject: Subject) -> i32 {
        match subject {
            Subject::Reading => 32,
            Subject::Math => 35,
        }
    }

    /// 是否为第二模块
    pub fn is_second_module(&self) -> bool {
        matches!(self, ModuleType::Module2E | ModuleType::Module2H)
    }
}

impl From<ModuleType> for String {
    fn from(module_type: ModuleType) -> Self {
        match module_type {
            ModuleType::Module1 => "1".to_string(),
            ModuleType::Module2E => "2E".to_string(),
            ModuleType::Module2H => "2H".to_string(),
        }
    }
}

impl From<String> for ModuleType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "1" => ModuleType::Module1,
            "2E" => ModuleType::Module2E,
            "2H" => ModuleType::Module2H,
            _ => ModuleType::Module1, // 默认值
        }
    }
}

/// 模块状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum ModuleStatus {
    /// 未开始
    NotStarted,
    /// 可开始（第一模块或第二模块已确定类型）
    Available,
    /// 等待中（第二模块等待第一模块完成）
    Pending,
    /// 进行中
    InProgress,
    /// 已完成
    Completed,
    /// 已提交
    Submitted,
}

impl ModuleStatus {
    /// 是否已完成
    pub fn is_completed(&self) -> bool {
        matches!(self, ModuleStatus::Completed | ModuleStatus::Submitted)
    }

    /// 是否可以开始
    pub fn is_available(&self) -> bool {
        matches!(self, ModuleStatus::Available)
    }

    /// 是否正在进行
    pub fn is_in_progress(&self) -> bool {
        matches!(self, ModuleStatus::InProgress)
    }

    /// 是否等待中
    pub fn is_pending(&self) -> bool {
        matches!(self, ModuleStatus::Pending)
    }
}

impl fmt::Display for ModuleStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ModuleStatus::NotStarted => write!(f, "not_started"),
            ModuleStatus::Available => write!(f, "available"),
            ModuleStatus::Pending => write!(f, "pending"),
            ModuleStatus::InProgress => write!(f, "in_progress"),
            ModuleStatus::Completed => write!(f, "completed"),
            ModuleStatus::Submitted => write!(f, "submitted"),
        }
    }
}

impl From<ModuleStatus> for String {
    fn from(status: ModuleStatus) -> Self {
        match status {
            ModuleStatus::NotStarted => "not_started".to_string(),
            ModuleStatus::Available => "available".to_string(),
            ModuleStatus::Pending => "pending".to_string(),
            ModuleStatus::InProgress => "in_progress".to_string(),
            ModuleStatus::Completed => "completed".to_string(),
            ModuleStatus::Submitted => "submitted".to_string(),
        }
    }
}

impl From<String> for ModuleStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "not_started" => ModuleStatus::NotStarted,
            "available" => ModuleStatus::Available,
            "pending" => ModuleStatus::Pending,
            "in_progress" => ModuleStatus::InProgress,
            "completed" => ModuleStatus::Completed,
            "submitted" => ModuleStatus::Submitted,
            _ => ModuleStatus::NotStarted, // 默认值
        }
    }
}



/// 答题状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum AnswerStatus {
    /// 已答
    Answered,
    /// 未答
    Unanswered,
    /// 跳过
    Skipped,
}

impl fmt::Display for AnswerStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AnswerStatus::Answered => write!(f, "answered"),
            AnswerStatus::Unanswered => write!(f, "unanswered"),
            AnswerStatus::Skipped => write!(f, "skipped"),
        }
    }
}

impl From<AnswerStatus> for String {
    fn from(status: AnswerStatus) -> Self {
        match status {
            AnswerStatus::Answered => "answered".to_string(),
            AnswerStatus::Unanswered => "unanswered".to_string(),
            AnswerStatus::Skipped => "skipped".to_string(),
        }
    }
}

impl From<String> for AnswerStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "answered" => AnswerStatus::Answered,
            "unanswered" => AnswerStatus::Unanswered,
            "skipped" => AnswerStatus::Skipped,
            _ => AnswerStatus::Unanswered, // 默认值
        }
    }
}

/// 模块信息值对象
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleInfo {
    /// 模块类型
    pub module_type: ModuleType,
    /// 学科
    pub subject: Subject,
    /// 题目数量
    pub question_count: i32,
    /// 时间限制（分钟）
    pub time_limit_minutes: i32,
    /// 难度级别描述
    pub difficulty_level: String,
}

impl ModuleInfo {
    /// 创建模块信息
    pub fn new(module_type: ModuleType, subject: Subject) -> Self {
        let difficulty_level = match module_type {
            ModuleType::Module1 => "standard".to_string(),
            ModuleType::Module2E => "easier".to_string(),
            ModuleType::Module2H => "harder".to_string(),
        };

        Self {
            module_type,
            subject,
            question_count: module_type.question_count(subject),
            time_limit_minutes: module_type.time_limit_minutes(subject),
            difficulty_level,
        }
    }
}

/// 会话配置值对象
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionConfig {
    /// 考试类型
    pub exam_type: ExamType,
    /// 总题数
    pub total_questions: i32,
    /// 总时长（分钟）
    pub total_time_minutes: i32,
    /// 包含的学科
    pub subjects: Vec<Subject>,
}

impl SessionConfig {
    /// 从考试类型创建配置
    pub fn from_exam_type(exam_type: ExamType) -> Self {
        let subjects = match exam_type {
            ExamType::Full => vec![Subject::Reading, Subject::Math],
            ExamType::Reading => vec![Subject::Reading],
            ExamType::Math => vec![Subject::Math],
        };

        Self {
            exam_type,
            total_questions: exam_type.total_questions(),
            total_time_minutes: exam_type.total_time_minutes(),
            subjects,
        }
    }

    /// 获取学科的模块列表
    pub fn get_modules_for_subject(&self, subject: Subject) -> Vec<ModuleInfo> {
        if !self.subjects.contains(&subject) {
            return vec![];
        }

        vec![
            ModuleInfo::new(ModuleType::Module1, subject),
            // 第二模块的具体类型需要根据第一模块的表现来决定
            // 这里先返回简单版本，实际使用时需要自适应逻辑
            ModuleInfo::new(ModuleType::Module2E, subject),
        ]
    }
}

/// 分数值对象
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Score(u32);

impl Score {
    /// 创建分数值对象
    pub fn new(score: u32) -> Result<Self, String> {
        if score <= 1600 {
            Ok(Self(score))
        } else {
            Err("SAT分数不能超过1600".to_string())
        }
    }

    /// 创建数学分数
    pub fn math_score(score: u32) -> Result<Self, String> {
        if score <= 800 {
            Ok(Self(score))
        } else {
            Err("SAT数学分数不能超过800".to_string())
        }
    }

    /// 创建阅读分数
    pub fn reading_score(score: u32) -> Result<Self, String> {
        if score <= 800 {
            Ok(Self(score))
        } else {
            Err("SAT阅读分数不能超过800".to_string())
        }
    }

    /// 获取分数值
    pub fn value(&self) -> u32 {
        self.0
    }

    /// 是否为高分
    pub fn is_high_score(&self) -> bool {
        self.0 >= 1400
    }

    /// 计算与另一个分数的差值
    pub fn difference(&self, other: &Score) -> i32 {
        self.0 as i32 - other.0 as i32
    }
}

/// 正确率值对象
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AccuracyRate(f64);

impl AccuracyRate {
    /// 创建正确率值对象
    pub fn new(rate: f64) -> Result<Self, String> {
        if (0.0..=1.0).contains(&rate) {
            Ok(Self(rate))
        } else {
            Err("正确率必须在0.0-1.0之间".to_string())
        }
    }

    /// 从正确题数和总题数计算正确率
    pub fn from_counts(correct: u32, total: u32) -> Result<Self, String> {
        if total == 0 {
            return Err("总题数不能为0".to_string());
        }

        let rate = correct as f64 / total as f64;
        Self::new(rate)
    }

    /// 获取正确率值
    pub fn value(&self) -> f64 {
        self.0
    }

    /// 获取百分比形式
    pub fn percentage(&self) -> f64 {
        self.0 * 100.0
    }

    /// 是否为高正确率
    pub fn is_high_accuracy(&self) -> bool {
        self.0 >= 0.8
    }
}

/// 考试时长值对象
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Duration(u32); // 分钟

impl Duration {
    /// 创建考试时长值对象
    pub fn new(minutes: u32) -> Result<Self, String> {
        if minutes > 0 && minutes <= 600 { // 最多10小时
            Ok(Self(minutes))
        } else {
            Err("考试时长必须在1-600分钟之间".to_string())
        }
    }

    /// 创建SAT完整考试时长
    pub fn sat_full() -> Result<Self, String> {
        Self::new(180) // 3小时
    }

    /// 创建SAT数学模块时长
    pub fn sat_math_module() -> Result<Self, String> {
        Self::new(35)
    }

    /// 创建SAT阅读模块时长
    pub fn sat_reading_module() -> Result<Self, String> {
        Self::new(65)
    }

    /// 获取时长值（分钟）
    pub fn minutes(&self) -> u32 {
        self.0
    }

    /// 获取时长值（小时）
    pub fn hours(&self) -> f64 {
        self.0 as f64 / 60.0
    }

    /// 是否超时
    pub fn is_overtime(&self, expected: &Duration) -> bool {
        self.0 > expected.0
    }
}

/// 进步趋势值对象
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ProgressTrend {
    /// 进步中
    Improving,
    /// 下降中
    Declining,
    /// 稳定
    Stable,
}

impl ProgressTrend {
    /// 从分数变化计算趋势
    pub fn from_score_change(change: i32) -> Self {
        match change {
            x if x > 10 => ProgressTrend::Improving,
            x if x < -10 => ProgressTrend::Declining,
            _ => ProgressTrend::Stable,
        }
    }

    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            ProgressTrend::Improving => "improving",
            ProgressTrend::Declining => "declining",
            ProgressTrend::Stable => "stable",
        }
    }

    /// 获取趋势描述
    pub fn description(&self) -> &'static str {
        match self {
            ProgressTrend::Improving => "成绩正在提升",
            ProgressTrend::Declining => "成绩有所下降",
            ProgressTrend::Stable => "成绩保持稳定",
        }
    }

    /// 是否为正向趋势
    pub fn is_positive(&self) -> bool {
        matches!(self, ProgressTrend::Improving | ProgressTrend::Stable)
    }
}

/// 分页参数值对象
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct PaginationParams {
    /// 页码（从1开始）
    pub page: u64,
    /// 每页大小
    pub page_size: u64,
}

impl PaginationParams {
    /// 创建分页参数
    pub fn new(page: u64, page_size: u64) -> Result<Self, String> {
        if page == 0 {
            return Err("页码必须从1开始".to_string());
        }

        if page_size == 0 || page_size > 100 {
            return Err("每页大小必须在1-100之间".to_string());
        }

        Ok(Self { page, page_size })
    }

    /// 默认分页参数
    pub fn default() -> Self {
        Self {
            page: 1,
            page_size: 20,
        }
    }

    /// 计算偏移量
    pub fn offset(&self) -> u64 {
        (self.page - 1) * self.page_size
    }

    /// 计算限制数量
    pub fn limit(&self) -> u64 {
        self.page_size
    }
}
