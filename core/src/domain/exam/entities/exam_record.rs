//! 考试记录领域实体
//!
//! 定义考试记录相关的领域实体和值对象

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 考试记录聚合根
/// 
/// 表示用户在特定试卷上的考试记录统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamRecord {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 考试统计信息
    pub statistics: ExamStatistics,
    /// 最近考试详情
    pub last_exam_detail: Option<ExamDetail>,
    /// 进步趋势
    pub progress_trend: ProgressTrend,
}

/// 考试统计信息值对象
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamStatistics {
    /// 考试总次数
    pub exam_count: i32,
    /// 完成次数
    pub completed_count: i32,
    /// 最高分
    pub best_score: Option<u32>,
    /// 最新分数
    pub latest_score: Option<u32>,
    /// 平均分
    pub average_score: Option<f64>,
}

impl ExamStatistics {
    /// 创建新的统计信息
    pub fn new() -> Self {
        Self {
            exam_count: 0,
            completed_count: 0,
            best_score: None,
            latest_score: None,
            average_score: None,
        }
    }

    /// 是否有考试记录
    pub fn has_exams(&self) -> bool {
        self.exam_count > 0
    }

    /// 是否有完成的考试
    pub fn has_completed_exams(&self) -> bool {
        self.completed_count > 0
    }

    /// 完成率
    pub fn completion_rate(&self) -> f64 {
        if self.exam_count == 0 {
            0.0
        } else {
            self.completed_count as f64 / self.exam_count as f64
        }
    }
}

/// 考试详情值对象
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamDetail {
    /// 会话ID
    pub session_id: String,
    /// 总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
    /// 考试进度
    pub exam_progress: f64,
    /// 考试状态
    pub exam_status: ExamStatus,
    /// 各学科分数详情
    pub subject_scores: Option<HashMap<String, u32>>,
    /// 各模块分数详情
    pub module_scores: Option<HashMap<String, u32>>,
}

/// 进步趋势值对象
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressTrend {
    /// 是否在进步
    pub is_improving: bool,
    /// 分数变化
    pub score_change: i32,
    /// 趋势描述
    pub trend_description: String,
}

/// 考试类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExamType {
    /// 完整考试（阅读+数学）
    Full,
    /// 仅数学
    Math,
    /// 仅阅读
    Reading,
}

impl From<String> for ExamType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "full" => ExamType::Full,
            "math" => ExamType::Math,
            "reading" => ExamType::Reading,
            _ => ExamType::Full,
        }
    }
}

impl From<ExamType> for String {
    fn from(et: ExamType) -> Self {
        match et {
            ExamType::Full => "full".to_string(),
            ExamType::Math => "math".to_string(),
            ExamType::Reading => "reading".to_string(),
        }
    }
}

/// 考试状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExamStatus {
    /// 已完成
    Completed,
    /// 部分完成
    Partial,
    /// 已放弃
    Abandoned,
}

impl From<String> for ExamStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "completed" => ExamStatus::Completed,
            "partial" => ExamStatus::Partial,
            "abandoned" => ExamStatus::Abandoned,
            _ => ExamStatus::Completed,
        }
    }
}

impl From<ExamStatus> for String {
    fn from(es: ExamStatus) -> Self {
        match es {
            ExamStatus::Completed => "completed".to_string(),
            ExamStatus::Partial => "partial".to_string(),
            ExamStatus::Abandoned => "abandoned".to_string(),
        }
    }
}

/// 考试记录查询条件
#[derive(Debug, Clone)]
pub struct ExamRecordQuery {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID（可选）
    pub paper_id: Option<i64>,
    /// 考试类型（可选）
    pub exam_type: Option<ExamType>,
    /// 分页参数
    pub pagination: PaginationParams,
}

/// 分页参数
#[derive(Debug, Clone)]
pub struct PaginationParams {
    /// 页码（从1开始）
    pub page: u32,
    /// 每页大小
    pub page_size: u32,
}

impl PaginationParams {
    /// 计算偏移量
    pub fn offset(&self) -> u32 {
        (self.page - 1) * self.page_size
    }
    
    /// 获取限制数量
    pub fn limit(&self) -> u32 {
        self.page_size
    }
}

/// 考试记录列表结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamRecordList {
    /// 考试记录列表
    pub records: Vec<ExamRecord>,
    /// 总记录数
    pub total_count: u64,
    /// 当前页码
    pub current_page: u32,
    /// 每页大小
    pub page_size: u32,
    /// 总页数
    pub total_pages: u32,
}

impl ExamRecordList {
    /// 创建新的考试记录列表
    pub fn new(
        records: Vec<ExamRecord>,
        total_count: u64,
        current_page: u32,
        page_size: u32,
    ) -> Self {
        let total_pages = if total_count == 0 {
            0
        } else {
            ((total_count - 1) / page_size as u64 + 1) as u32
        };
        
        Self {
            records,
            total_count,
            current_page,
            page_size,
            total_pages,
        }
    }
}

impl ExamRecord {
    /// 创建新的考试记录
    pub fn new(
        user_id: i64,
        paper_id: i64,
        paper_name: String,
        exam_type: ExamType,
    ) -> Self {
        Self {
            user_id,
            paper_id,
            paper_name,
            exam_type,
            statistics: ExamStatistics::new(),
            last_exam_detail: None,
            progress_trend: ProgressTrend {
                is_improving: false,
                score_change: 0,
                trend_description: "暂无数据".to_string(),
            },
        }
    }
    
    /// 更新统计信息
    pub fn update_statistics(&mut self, statistics: ExamStatistics) {
        self.statistics = statistics;
    }
    
    /// 更新最近考试详情
    pub fn update_last_exam_detail(&mut self, detail: ExamDetail) {
        self.last_exam_detail = Some(detail);
    }
    
    /// 更新进步趋势
    pub fn update_progress_trend(&mut self, trend: ProgressTrend) {
        self.progress_trend = trend;
    }
    
    /// 是否有考试记录
    pub fn has_exam_records(&self) -> bool {
        self.statistics.exam_count > 0
    }
    
    /// 是否有完成的考试
    pub fn has_completed_exams(&self) -> bool {
        self.statistics.completed_count > 0
    }
}
