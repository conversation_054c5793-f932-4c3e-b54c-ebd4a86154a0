//! 考试记录实体转换器
//!
//! 提供统一的转换逻辑，处理领域层 ExamRecord 实体与其他层 DTO 之间的转换
//! 避免在多个地方重复转换代码，确保转换逻辑的一致性

use chrono::{DateTime, Utc};
use std::collections::HashMap;

use super::entities::exam_record::{
    ExamRecord, ExamStatistics, ExamDetail, ProgressTrend, ExamType, ExamStatus
};
use crate::error::{Error, Result};

/// 考试记录转换器
/// 
/// 统一处理 ExamRecord 实体与各种 DTO、模型之间的转换
pub struct ExamRecordConverter;

impl ExamRecordConverter {
    /// 创建新的转换器实例
    pub fn new() -> Self {
        Self
    }

    /// 从数据库统计模型转换为领域实体
    pub fn from_statistics_model(
        user_id: i64,
        paper_id: i64,
        paper_name: String,
        exam_type: ExamType,
        exam_count: i32,
        completed_count: i32,
        best_score: Option<u32>,
        latest_score: Option<u32>,
        average_score: Option<f64>,
        score_change: i32,
        trend_description: String,
    ) -> ExamRecord {
        let statistics = ExamStatistics {
            exam_count,
            completed_count,
            best_score,
            latest_score,
            average_score,
        };

        let progress_trend = ProgressTrend {
            is_improving: score_change > 0,
            score_change,
            trend_description,
        };

        let mut exam_record = ExamRecord::new(user_id, paper_id, paper_name, exam_type);
        exam_record.update_statistics(statistics);
        exam_record.update_progress_trend(progress_trend);

        exam_record
    }

    /// 从考试历史模型转换为考试详情
    pub fn from_exam_history_model(
        session_id: String,
        total_score: Option<u32>,
        reading_score: Option<u32>,
        math_score: Option<u32>,
        accuracy_rate: Option<f64>,
        completed_at: Option<DateTime<Utc>>,
        duration_minutes: Option<u32>,
        exam_progress: f64,
        exam_status: ExamStatus,
        subject_scores: Option<HashMap<String, u32>>,
        module_scores: Option<HashMap<String, u32>>,
    ) -> ExamDetail {
        ExamDetail {
            session_id,
            total_score,
            reading_score,
            math_score,
            accuracy_rate,
            completed_at,
            duration_minutes,
            exam_progress,
            exam_status,
            subject_scores,
            module_scores,
        }
    }

    /// 计算进步趋势
    pub fn calculate_progress_trend(
        current_score: Option<u32>,
        previous_score: Option<u32>,
    ) -> ProgressTrend {
        match (current_score, previous_score) {
            (Some(current), Some(previous)) => {
                let change = current as i32 - previous as i32;
                ProgressTrend {
                    is_improving: change > 0,
                    score_change: change,
                    trend_description: match change {
                        x if x > 50 => "显著提升".to_string(),
                        x if x > 10 => "稳步提升".to_string(),
                        x if x > -10 => "保持稳定".to_string(),
                        x if x > -50 => "略有下降".to_string(),
                        _ => "明显下降".to_string(),
                    },
                }
            }
            _ => ProgressTrend {
                is_improving: false,
                score_change: 0,
                trend_description: "暂无数据".to_string(),
            },
        }
    }

    /// 验证分数范围
    pub fn validate_score(score: Option<u32>) -> Result<Option<u32>> {
        match score {
            Some(s) if s > 1600 => Err(Error::InvalidInput("分数不能超过1600".to_string())),
            _ => Ok(score),
        }
    }

    /// 验证正确率范围
    pub fn validate_accuracy_rate(rate: Option<f64>) -> Result<Option<f64>> {
        match rate {
            Some(r) if r < 0.0 || r > 1.0 => Err(Error::InvalidInput("正确率必须在0.0-1.0之间".to_string())),
            _ => Ok(rate),
        }
    }

    /// 从字符串转换考试类型
    pub fn parse_exam_type(exam_type_str: &str) -> Result<ExamType> {
        match exam_type_str.to_lowercase().as_str() {
            "full" => Ok(ExamType::Full),
            "math" => Ok(ExamType::Math),
            "reading" => Ok(ExamType::Reading),
            _ => Err(Error::InvalidInput(format!("不支持的考试类型: {}", exam_type_str))),
        }
    }

    /// 从字符串转换考试状态
    pub fn parse_exam_status(status_str: &str) -> Result<ExamStatus> {
        match status_str.to_lowercase().as_str() {
            "completed" => Ok(ExamStatus::Completed),
            "partial" => Ok(ExamStatus::Partial),
            "abandoned" => Ok(ExamStatus::Abandoned),
            _ => Err(Error::InvalidInput(format!("不支持的考试状态: {}", status_str))),
        }
    }

    /// 计算考试统计信息
    pub fn calculate_statistics(
        exam_scores: &[Option<u32>],
        completed_exams: usize,
    ) -> ExamStatistics {
        let exam_count = exam_scores.len() as i32;
        let completed_count = completed_exams as i32;
        
        let valid_scores: Vec<u32> = exam_scores.iter().filter_map(|&s| s).collect();
        
        let best_score = valid_scores.iter().max().copied();
        let latest_score = exam_scores.last().and_then(|&s| s);
        let average_score = if !valid_scores.is_empty() {
            Some(valid_scores.iter().sum::<u32>() as f64 / valid_scores.len() as f64)
        } else {
            None
        };

        ExamStatistics {
            exam_count,
            completed_count,
            best_score,
            latest_score,
            average_score,
        }
    }

    /// 格式化分数显示
    pub fn format_score(score: Option<u32>) -> String {
        match score {
            Some(s) => s.to_string(),
            None => "未完成".to_string(),
        }
    }

    /// 格式化正确率显示
    pub fn format_accuracy_rate(rate: Option<f64>) -> String {
        match rate {
            Some(r) => format!("{:.1}%", r * 100.0),
            None => "未完成".to_string(),
        }
    }

    /// 格式化考试时长显示
    pub fn format_duration(minutes: Option<u32>) -> String {
        match minutes {
            Some(m) => {
                let hours = m / 60;
                let mins = m % 60;
                if hours > 0 {
                    format!("{}小时{}分钟", hours, mins)
                } else {
                    format!("{}分钟", mins)
                }
            }
            None => "未完成".to_string(),
        }
    }
}

impl Default for ExamRecordConverter {
    fn default() -> Self {
        Self::new()
    }
}
