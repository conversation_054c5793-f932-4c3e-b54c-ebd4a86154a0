//! SAT考试会话实体模型
//!
//! 使用 SeaORM 定义的数据库实体

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// SAT考试会话实体
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "t_sat_mock_exam_session")]
pub struct Model {
    /// 会话ID，主键
    #[sea_orm(primary_key, auto_increment = false)]
    pub session_id: String,

    /// 用户ID
    pub user_id: i64,

    /// 试卷ID
    pub paper_id: i64,

    /// 考试类型：full, math, reading
    pub exam_type: String,

    /// 当前学科：reading, math
    pub current_subject: Option<String>,

    /// 当前模块类型：1, 2E, 2H
    pub current_module_type: Option<String>,

    /// 会话状态：in_progress, completed
    pub session_status: String,

    /// 休息开始时间
    pub break_start_time: Option<DateTimeWithTimeZone>,

    /// 休息结束时间
    pub break_end_time: Option<DateTimeWithTimeZone>,

    /// 总用时（秒）
    pub total_time_seconds: Option<i32>,

    /// 语言部分用时（秒）
    pub reading_time_seconds: Option<i32>,

    /// 数学部分用时（秒）
    pub math_time_seconds: Option<i32>,

    /// 创建时间
    pub created_at: DateTimeWithTimeZone,

    /// 更新时间
    pub updated_at: DateTimeWithTimeZone,

    /// 完成时间
    pub completed_at: Option<DateTimeWithTimeZone>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}

/// 考试类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExamType {
    Full,
    Math,
    Reading,
}

impl From<String> for ExamType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "full" => ExamType::Full,
            "math" => ExamType::Math,
            "reading" => ExamType::Reading,
            _ => ExamType::Full, // 默认值
        }
    }
}

impl From<ExamType> for String {
    fn from(et: ExamType) -> Self {
        match et {
            ExamType::Full => "full".to_string(),
            ExamType::Math => "math".to_string(),
            ExamType::Reading => "reading".to_string(),
        }
    }
}

/// 学科类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum Subject {
    Reading,
    Math,
}

impl From<String> for Subject {
    fn from(s: String) -> Self {
        match s.as_str() {
            "reading" => Subject::Reading,
            "math" => Subject::Math,
            _ => Subject::Reading, // 默认值
        }
    }
}

impl From<Subject> for String {
    fn from(s: Subject) -> Self {
        match s {
            Subject::Reading => "reading".to_string(),
            Subject::Math => "math".to_string(),
        }
    }
}

/// 会话状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SessionStatus {
    InProgress,
    Completed,
}

impl From<String> for SessionStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "in_progress" => SessionStatus::InProgress,
            "completed" => SessionStatus::Completed,
            _ => SessionStatus::InProgress, // 默认值
        }
    }
}

impl From<SessionStatus> for String {
    fn from(ss: SessionStatus) -> Self {
        match ss {
            SessionStatus::InProgress => "in_progress".to_string(),
            SessionStatus::Completed => "completed".to_string(),
        }
    }
}

/// 模块类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ModuleType {
    Module1,
    Module2E,
    Module2H,
}

impl ModuleType {
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "1" => Some(ModuleType::Module1),
            "2E" => Some(ModuleType::Module2E),
            "2H" => Some(ModuleType::Module2H),
            _ => None,
        }
    }
}

impl From<ModuleType> for String {
    fn from(mt: ModuleType) -> Self {
        match mt {
            ModuleType::Module1 => "1".to_string(),
            ModuleType::Module2E => "2E".to_string(),
            ModuleType::Module2H => "2H".to_string(),
        }
    }
}
