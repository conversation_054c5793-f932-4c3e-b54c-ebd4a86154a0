//! SAT试卷实体模型
//!
//! 使用 SeaORM 定义的数据库实体

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// SAT试卷实体
#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "t_sat_paper")]
pub struct Model {
    /// 试卷ID，主键
    #[sea_orm(primary_key)]
    pub paper_id: i64,

    /// 试卷名称
    #[sea_orm(column_name = "papar_name")]
    pub paper_name: String,

    /// 试卷内部名称
    pub paper_inner_name: String,

    /// 版本
    pub version: i32,

    /// 状态
    pub status: i32,

    /// 学科ID
    pub subject_id: Option<i32>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::sat_paper_question::Entity")]
    SatPaperQuestion,
}

impl Related<super::sat_paper_question::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SatPaperQuestion.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
