//! 考试统计实体模型
//!
//! 使用 SeaORM 定义的数据库实体

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// 考试统计实体
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "t_sat_exam_statistics")]
pub struct Model {
    /// 主键ID
    #[sea_orm(primary_key)]
    pub id: i64,

    /// 用户ID
    pub user_id: i64,

    /// 试卷ID
    pub paper_id: i64,

    /// 试卷名称
    pub paper_name: String,

    /// 考试类型：full, math, reading
    pub exam_type: String,

    /// 考试总次数
    pub exam_count: i32,

    /// 完成次数
    pub completed_count: i32,

    /// 部分完成次数
    pub partial_count: i32,

    /// 最高分
    pub best_score: Option<i32>,

    /// 最新分数
    pub latest_score: Option<i32>,

    /// 平均分
    pub average_score: Option<Decimal>,

    /// 分数趋势：improving, declining, stable
    pub score_trend: Option<String>,

    /// 最近考试会话ID
    pub latest_session_id: Option<String>,

    /// 最近考试时间
    pub latest_exam_at: Option<DateTimeWithTimeZone>,

    /// 分数变化（与上次相比）
    pub score_change: i32,

    /// 趋势描述
    pub trend_description: Option<String>,

    /// 总学习时间（分钟）
    pub total_study_time_minutes: i32,

    /// 平均考试时长（分钟）
    pub average_duration_minutes: Option<i32>,

    /// 创建时间
    pub created_at: DateTimeWithTimeZone,

    /// 更新时间
    pub updated_at: DateTimeWithTimeZone,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}

/// 考试记录视图实体（对应 v_sat_exam_records 视图）
pub mod exam_record_view {
    use super::*;

    #[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
    #[sea_orm(table_name = "v_sat_exam_records")]
    pub struct Model {
    /// 用户ID
    #[sea_orm(primary_key, auto_increment = false)]
    pub user_id: i64,

    /// 试卷ID
    #[sea_orm(primary_key, auto_increment = false)]
    pub paper_id: i64,

    /// 考试类型
    #[sea_orm(primary_key, auto_increment = false)]
    pub exam_type: String,

    /// 试卷名称
    pub paper_name: String,

    /// 考试次数统计
    pub exam_count: i32,
    pub completed_count: i32,

    /// 分数统计
    pub best_score: Option<i32>,
    pub latest_score: Option<i32>,
    pub average_score: Option<Decimal>,
    pub score_change: i32,
    pub trend_description: Option<String>,

    /// 最近考试详情
    pub latest_session_id: Option<String>,
    pub latest_total_score: Option<i32>,
    pub latest_reading_score: Option<i32>,
    pub latest_math_score: Option<i32>,
    pub latest_accuracy_rate: Option<Decimal>,
    pub latest_completed_at: Option<DateTimeWithTimeZone>,
    pub latest_duration_minutes: Option<i32>,
    pub latest_exam_progress: Option<Decimal>,
    pub latest_exam_status: Option<String>,

    /// 进步趋势
    pub is_improving: Option<bool>,
    }

    #[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
    pub enum Relation {}

    impl ActiveModelBehavior for ActiveModel {}
}

/// 考试类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExamType {
    Full,
    Math,
    Reading,
}

impl From<String> for ExamType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "full" => ExamType::Full,
            "math" => ExamType::Math,
            "reading" => ExamType::Reading,
            _ => ExamType::Full,
        }
    }
}

impl From<ExamType> for String {
    fn from(et: ExamType) -> Self {
        match et {
            ExamType::Full => "full".to_string(),
            ExamType::Math => "math".to_string(),
            ExamType::Reading => "reading".to_string(),
        }
    }
}

/// 分数趋势枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ScoreTrend {
    Improving,
    Declining,
    Stable,
}

impl From<String> for ScoreTrend {
    fn from(s: String) -> Self {
        match s.as_str() {
            "improving" => ScoreTrend::Improving,
            "declining" => ScoreTrend::Declining,
            "stable" => ScoreTrend::Stable,
            _ => ScoreTrend::Stable,
        }
    }
}

impl From<ScoreTrend> for String {
    fn from(st: ScoreTrend) -> Self {
        match st {
            ScoreTrend::Improving => "improving".to_string(),
            ScoreTrend::Declining => "declining".to_string(),
            ScoreTrend::Stable => "stable".to_string(),
        }
    }
}

/// 考试状态枚举（基于现有的 exam_status 字段）
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExamStatus {
    Completed,
    Abandoned,
    Timeout,
}

impl From<String> for ExamStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "completed" => ExamStatus::Completed,
            "abandoned" => ExamStatus::Abandoned,
            "timeout" => ExamStatus::Timeout,
            _ => ExamStatus::Completed,
        }
    }
}

impl From<ExamStatus> for String {
    fn from(es: ExamStatus) -> Self {
        match es {
            ExamStatus::Completed => "completed".to_string(),
            ExamStatus::Abandoned => "abandoned".to_string(),
            ExamStatus::Timeout => "timeout".to_string(),
        }
    }
}
