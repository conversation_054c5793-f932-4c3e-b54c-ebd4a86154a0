//! SAT试卷题目实体模型
//!
//! 使用 SeaORM 定义的数据库实体

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

pub type DateTimeWithTimeZone = DateTime<Utc>;

/// SAT试卷题目实体
#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "t_sat_paper_question")]
pub struct Model {
    /// 主键ID
    #[sea_orm(primary_key)]
    pub id: i32,

    /// 试卷ID
    pub paper_id: i64,

    /// 版本号
    pub version: i32,

    /// 题目ID
    pub question_id: i32,

    /// 学科ID
    pub subject_id: Option<i32>,

    /// 模块类型：1, 2E, 2H
    pub module_type: String,

    /// 模块内序号
    pub module_sequence: i32,

    /// 状态
    pub status: i32,

    /// 创建时间
    pub created_at: DateTimeWithTimeZone,

    /// 更新时间
    pub updated_at: DateTimeWithTimeZone,

    /// 学科组ID
    pub subject_group_id: Option<i32>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::sat_paper::Entity",
        from = "Column::PaperId",
        to = "super::sat_paper::Column::PaperId"
    )]
    SatPaper,
    #[sea_orm(
        belongs_to = "super::question::Entity",
        from = "Column::QuestionId",
        to = "super::question::Column::QuestionId"
    )]
    Question,
}

impl Related<super::sat_paper::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SatPaper.def()
    }
}

impl Related<super::question::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Question.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
