//! SAT考试模块进度实体模型
//!
//! 使用 SeaORM 定义的数据库实体

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// SAT考试模块进度实体
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "t_sat_mock_exam_module_progress")]
pub struct Model {
    /// 主键ID
    #[sea_orm(primary_key)]
    pub id: i64,

    /// 会话ID
    pub session_id: String,

    /// 用户ID
    pub user_id: i64,

    /// 试卷ID
    pub paper_id: i64,

    /// 模块类型：1, 2E, 2H
    pub module_type: String,

    /// 学科：reading, math
    pub subject: String,

    /// 模块总题数
    pub total_questions: i32,

    /// 已答题数
    pub answered_questions: Option<i32>,

    /// 答对题数
    pub correct_questions: Option<i32>,

    /// 时间限制（秒）
    pub time_limit_seconds: i32,

    /// 已用时间（秒）
    pub time_used_seconds: Option<i32>,

    /// 剩余时间（秒）- 状态恢复的关键字段
    pub remaining_time_seconds: Option<i32>,

    /// 模块状态：not_started=未开始, in_progress=进行中, completed=已完成, submitted=已提交
    pub module_status: String,

    /// 开始时间
    pub started_at: Option<DateTimeWithTimeZone>,

    /// 完成时间
    pub completed_at: Option<DateTimeWithTimeZone>,

    /// 创建时间
    pub created_at: DateTimeWithTimeZone,

    /// 更新时间
    pub updated_at: DateTimeWithTimeZone,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}

/// 模块状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ModuleStatus {
    NotStarted,
    InProgress,
    Completed,
    Submitted,
}

impl From<String> for ModuleStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "not_started" => ModuleStatus::NotStarted,
            "in_progress" => ModuleStatus::InProgress,
            "completed" => ModuleStatus::Completed,
            "submitted" => ModuleStatus::Submitted,
            _ => ModuleStatus::NotStarted, // 默认值
        }
    }
}

impl From<ModuleStatus> for String {
    fn from(status: ModuleStatus) -> Self {
        match status {
            ModuleStatus::NotStarted => "not_started".to_string(),
            ModuleStatus::InProgress => "in_progress".to_string(),
            ModuleStatus::Completed => "completed".to_string(),
            ModuleStatus::Submitted => "submitted".to_string(),
        }
    }
}

/// 学科类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum Subject {
    Reading,
    Math,
}

impl From<String> for Subject {
    fn from(s: String) -> Self {
        match s.as_str() {
            "reading" => Subject::Reading,
            "math" => Subject::Math,
            _ => Subject::Reading, // 默认值
        }
    }
}

impl From<Subject> for String {
    fn from(subject: Subject) -> Self {
        match subject {
            Subject::Reading => "reading".to_string(),
            Subject::Math => "math".to_string(),
        }
    }
}

/// 模块类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ModuleType {
    Module1,
    Module2E,
    Module2H,
}

impl From<String> for ModuleType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "1" => ModuleType::Module1,
            "2E" => ModuleType::Module2E,
            "2H" => ModuleType::Module2H,
            _ => ModuleType::Module1, // 默认值
        }
    }
}

impl From<ModuleType> for String {
    fn from(module_type: ModuleType) -> Self {
        match module_type {
            ModuleType::Module1 => "1".to_string(),
            ModuleType::Module2E => "2E".to_string(),
            ModuleType::Module2H => "2H".to_string(),
        }
    }
}
