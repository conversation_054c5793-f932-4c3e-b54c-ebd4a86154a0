//! SAT考试答题记录实体模型
//!
//! 使用 SeaORM 定义的数据库实体

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// SAT考试答题记录实体
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "t_sat_mock_exam_answer")]
pub struct Model {
    /// 主键ID
    #[sea_orm(primary_key)]
    pub id: i64,

    /// 会话ID，关联t_sat_mock_exam_session表
    pub session_id: String,

    /// 用户ID
    pub user_id: i64,

    /// 试卷ID
    pub paper_id: i64,

    /// 题目ID，关联t_sat_question表
    pub question_id: i32,

    /// 模块类型：1, 2E, 2H
    pub module_type: String,

    /// 题目在模块中的顺序
    pub module_sequence: i32,

    /// 用户答案
    pub user_answer: Option<String>,

    /// 是否正确
    pub is_correct: Option<bool>,

    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,

    /// 答题状态：answered=已答, unanswered=未答, skipped=跳过
    pub answer_status: String,

    /// 创建时间
    pub created_at: DateTimeWithTimeZone,

    /// 更新时间
    pub updated_at: DateTimeWithTimeZone,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}

/// 答题状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AnswerStatus {
    Answered,
    Unanswered,
    Skipped,
}

impl From<String> for AnswerStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "answered" => AnswerStatus::Answered,
            "unanswered" => AnswerStatus::Unanswered,
            "skipped" => AnswerStatus::Skipped,
            _ => AnswerStatus::Unanswered, // 默认值
        }
    }
}

impl From<AnswerStatus> for String {
    fn from(status: AnswerStatus) -> Self {
        match status {
            AnswerStatus::Answered => "answered".to_string(),
            AnswerStatus::Unanswered => "unanswered".to_string(),
            AnswerStatus::Skipped => "skipped".to_string(),
        }
    }
}

/// 模块类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ModuleType {
    Module1,
    Module2E,
    Module2H,
}

impl From<String> for ModuleType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "1" => ModuleType::Module1,
            "2E" => ModuleType::Module2E,
            "2H" => ModuleType::Module2H,
            _ => ModuleType::Module1, // 默认值
        }
    }
}

impl From<ModuleType> for String {
    fn from(module_type: ModuleType) -> Self {
        match module_type {
            ModuleType::Module1 => "1".to_string(),
            ModuleType::Module2E => "2E".to_string(),
            ModuleType::Module2H => "2H".to_string(),
        }
    }
}
