//！ 不要用这个 
//! StorageManager违反了DDD架构


use std::sync::Arc;
use std::time::Duration;
use tokio::sync::OnceCell;
use tokio::time::sleep;

use crate::config::PostgresConfig;
use crate::algorithms::elo::config::EloConfig;

use crate::services::elo::knowledge_elo::{KnowledgeEloConfig, KnowledgeEloService};
use crate::services::elo::service::EloServiceConfig;
use crate::storage::elo::{
    SeaOrmEloFactorsStorage, SeaOrmEloHistoryStorage, SeaOrmKnowledgeEncounterStorage,
    SeaOrmStreakStorage,
};
use crate::storage::entities::SeaOrmAbilityStorage;

use crate::infrastructure::persistence::storage_impl::elo::SeaOrmKnowledgeEloStorage;
use crate::storage::traits::combined::CombinedEloStorage;
use crate::storage::{AbilityStorage, EloFactorsStorage, Storage};
use crate::{Error, Result};
use sea_orm::{ConnectionTrait, Database, DatabaseConnection};
use tracing::error;

/// 存储管理器
pub static STORAGE_MANAGER: OnceCell<Arc<StorageManager>> = OnceCell::const_new();

/// 存储管理器
#[derive(Debug, Clone)]
pub struct StorageManager {
    sea_orm_db: Arc<DatabaseConnection>,
    orm_ability: Arc<SeaOrmAbilityStorage>,
    orm_knowledge_elo: Arc<SeaOrmKnowledgeEloStorage>,

    // ELO相关存储实现
    orm_elo_history: Arc<SeaOrmEloHistoryStorage>,
    orm_streak: Arc<SeaOrmStreakStorage>,
    orm_knowledge_encounter: Arc<SeaOrmKnowledgeEncounterStorage>,
    orm_elo_factors: Arc<SeaOrmEloFactorsStorage>,

    /// 存储配置信息
    config: PostgresConfig,
}

impl StorageManager {
    /// 创建一个空的StorageManager，用于测试或不需要数据库的情况
    pub fn new_empty() -> Self {
        // 创建一个内存中的数据库连接，不会实际连接到数据库
        // 这仅用于编译通过，不要实际调用其方法
        let sea_orm_db = Arc::new(sea_orm::DatabaseConnection::default());

        // 创建空的存储实现
        let orm_ability = Arc::new(SeaOrmAbilityStorage::new_empty());
        let orm_knowledge_elo = Arc::new(SeaOrmKnowledgeEloStorage::new_empty());

        // 创建空的ELO相关存储实现
        let orm_elo_history = Arc::new(SeaOrmEloHistoryStorage::new((*sea_orm_db).clone()));
        let orm_streak = Arc::new(SeaOrmStreakStorage::new((*sea_orm_db).clone()));
        let orm_knowledge_encounter =
            Arc::new(SeaOrmKnowledgeEncounterStorage::new((*sea_orm_db).clone()));
        let orm_elo_factors = Arc::new(SeaOrmEloFactorsStorage::new((*sea_orm_db).clone()));

        Self {
            sea_orm_db,
            orm_ability,
            orm_knowledge_elo,
            orm_elo_history,
            orm_streak,
            orm_knowledge_encounter,
            orm_elo_factors,
            config: PostgresConfig::default(),
        }
    }

    /// 创建新的存储管理器
    pub async fn new(pg_config: PostgresConfig) -> Result<Self> {
        // 创建Sea-ORM连接
        let mut db_opts = sea_orm::ConnectOptions::new(&pg_config.url);

        // 正确设置参数 - 设置为链式调用而不是重新赋值
        db_opts
            .max_connections(pg_config.max_connections as u32)
            .min_connections(pg_config.min_connections as u32)
            .connect_timeout(Duration::from_secs(pg_config.connection_timeout))
            .sqlx_logging(true);

        // 单独处理可选参数
        if let Some(idle_timeout) = pg_config.idle_timeout {
            db_opts.idle_timeout(Duration::from_secs(idle_timeout));
        }

        // 设置最大生命周期
        db_opts.max_lifetime(Duration::from_secs(3600));

        let sea_orm_db = Database::connect(db_opts)
            .await
            .map_err(|e| Error::storage(format!("数据库连接失败: {}", e)))?;

        // 将DatabaseConnection包装为Arc，供后续使用
        let sea_orm_db = Arc::new(sea_orm_db);

        // 创建ORM存储实现 - 使用原始DatabaseConnection
        let orm_ability = SeaOrmAbilityStorage::new((*sea_orm_db).clone());

        // 获取EloConfig
        let elo_config =
            crate::algorithms::elo::config::EloConfig::from_global_config().unwrap_or_default();
        let orm_knowledge_elo = SeaOrmKnowledgeEloStorage::new((*sea_orm_db).clone(), elo_config);

        // 创建ELO相关存储实现
        let orm_elo_history = SeaOrmEloHistoryStorage::new((*sea_orm_db).clone());
        let orm_streak = SeaOrmStreakStorage::new((*sea_orm_db).clone());
        let orm_knowledge_encounter = SeaOrmKnowledgeEncounterStorage::new((*sea_orm_db).clone());
        let orm_elo_factors = SeaOrmEloFactorsStorage::new((*sea_orm_db).clone());

        // 验证连接健康
        orm_ability.health_check().await?;

        Ok(Self {
            sea_orm_db,
            orm_ability: Arc::new(orm_ability),
            orm_knowledge_elo: Arc::new(orm_knowledge_elo),
            orm_elo_history: Arc::new(orm_elo_history),
            orm_streak: Arc::new(orm_streak),
            orm_knowledge_encounter: Arc::new(orm_knowledge_encounter),
            orm_elo_factors: Arc::new(orm_elo_factors),
            config: pg_config,
        })
    }

    /// 获取连接池统计信息
    pub fn connection_stats(&self) -> ConnectionStats {
        ConnectionStats {
            max_connections: self.config.max_connections,
            min_connections: self.config.min_connections,
            idle_timeout: self.config.idle_timeout,
            statement_timeout: self.config.statement_timeout,
        }
    }

    /// 从全局配置创建存储管理器
    pub async fn from_config() -> Result<Self> {
        // Load the global core configuration
        let core_config = crate::config::global()
            .map_err(|e| Error::config(format!("Failed to load global config: {}", e)))?;

        // 尝试获取配置文件中指定的PostgreSQL配置
        // 首先尝试 "main" 键
        let mut pg_config = core_config.get_postgres_config("main");

        // 如果没有找到，尝试 "main_postgres" 键
        if pg_config.is_none() {
            pg_config = core_config.get_postgres_config("main_postgres");
            if pg_config.is_some() {
                tracing::info!("使用 'main_postgres' 配置");
            }
        }

        // 如果都没有找到，尝试其他任何已启用的PostgreSQL配置
        if pg_config.is_none() {
            // Config::get_postgres_config 方法会自动寻找第一个可用的PostgreSQL配置
            pg_config = core_config.get_postgres_config("any");
            if pg_config.is_some() {
                tracing::info!("找不到主数据库配置，使用第一个可用的PostgreSQL配置");
            }
        }

        // 如果仍然没有找到，返回错误
        let pg_config = pg_config
            .ok_or_else(|| Error::config("在配置中找不到任何可用的PostgreSQL配置".to_string()))?;

        // Create a new instance using the loaded config
        Self::new(pg_config.clone()).await
    }

    /// 获取能力值存储
    pub fn ability_storage(&self) -> Arc<dyn AbilityStorage + Send + Sync> {
        self.orm_ability.clone() as Arc<dyn AbilityStorage + Send + Sync>
    }

    /// 获取知识点Elo存储
    pub fn knowledge_elo_storage(&self) -> Arc<dyn CombinedEloStorage + Send + Sync> {
        self.orm_knowledge_elo.clone() as Arc<dyn CombinedEloStorage + Send + Sync>
    }



    /// 获取SeaORM数据库连接
    pub fn sea_orm_db(&self) -> Arc<DatabaseConnection> {
        self.sea_orm_db.clone()
    }

    /// 全局初始化数据库
    pub async fn initialize_global() -> Result<Arc<Self>> {
        let manager = Self::from_config().await?;
        let arc_manager = Arc::new(manager);

        match STORAGE_MANAGER.set(arc_manager.clone()) {
            Ok(_) => Ok(arc_manager),
            Err(_) => Err(Error::storage("存储管理器已经初始化")),
        }
    }

    /// 使用特定配置全局初始化数据库
    pub async fn initialize_global_with_config(pg_config: PostgresConfig) -> Result<Arc<Self>> {
        let manager = Self::new(pg_config).await?;
        let arc_manager = Arc::new(manager);

        match STORAGE_MANAGER.set(arc_manager.clone()) {
            Ok(_) => Ok(arc_manager),
            Err(_) => Err(Error::storage("存储管理器已经初始化")),
        }
    }

    /// 获取全局实例
    pub fn global() -> Option<Arc<Self>> {
        STORAGE_MANAGER.get().cloned()
    }

    /// 创建知识点Elo能力服务
    pub fn create_knowledge_elo_service(&self, config: EloServiceConfig) -> KnowledgeEloService {
        KnowledgeEloService::new(
            (*self.sea_orm_db).clone(),
            self.knowledge_elo_storage(),
            KnowledgeEloConfig::new(
                &config.name,
                config.elo_config,
                config.cache_ttl_seconds,
                config.use_dynamic_k,
                config.min_k_ratio,
            ),
            Some(self.elo_factors_storage()),
        )
    }

    /// 从EloConfig创建知识点Elo能力服务
    pub fn create_knowledge_elo_service_from_config(
        &self,
        elo_config: &EloConfig,
    ) -> KnowledgeEloService {
        KnowledgeEloService::from_elo_config(
            (*self.sea_orm_db).clone(),
            self.knowledge_elo_storage(),
            elo_config,
            Some(self.elo_factors_storage()),
        )
    }

    /// 执行健康检查
    pub async fn execute_health_check(&self) -> Result<()> {
        // 使用重试机制执行健康检查
        let max_retries = 2;
        let mut retry_count = 0;
        let mut last_error = None;

        while retry_count < max_retries {
            let conn = self.sea_orm_db.as_ref();
            let result = sea_orm::Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                "SELECT 1 as health_check",
            )
            .to_owned();

            match conn.query_one(result).await {
                Ok(_) => return Ok(()),
                Err(e) => {
                    retry_count += 1;
                    last_error = Some(e.to_string());

                    error!(
                        "健康检查失败 ({}/{}): {}",
                        retry_count,
                        max_retries,
                        last_error.as_ref().unwrap()
                    );

                    if retry_count < max_retries {
                        let backoff = Duration::from_millis(100 * (1 << retry_count));
                        sleep(backoff).await;
                    }
                }
            }
        }

        Err(Error::storage(format!(
            "数据库健康检查失败: {}",
            last_error.unwrap_or_else(|| "未知错误".to_string())
        )))
    }

    /// 检查是否有可用的存储
    pub fn storage_available(&self) -> bool {
        // 检查sea_orm_db是否不是Disconnected变体
        !matches!(
            self.sea_orm_db.as_ref(),
            sea_orm::DatabaseConnection::Disconnected
        )
    }

    /// 获取ELO历史记录存储
    pub fn elo_history_storage(&self) -> Arc<SeaOrmEloHistoryStorage> {
        self.orm_elo_history.clone()
    }

    /// 获取连胜连败记录存储
    pub fn streak_storage(&self) -> Arc<SeaOrmStreakStorage> {
        self.orm_streak.clone()
    }

    /// 获取知识点接触记录存储
    pub fn knowledge_encounter_storage(&self) -> Arc<SeaOrmKnowledgeEncounterStorage> {
        self.orm_knowledge_encounter.clone()
    }

    /// 获取ELO因子存储
    pub fn elo_factors_storage(&self) -> Arc<dyn EloFactorsStorage + Send + Sync> {
        self.orm_elo_factors.clone() as Arc<dyn EloFactorsStorage + Send + Sync>
    }
}

/// 连接池统计信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct ConnectionStats {
    /// 最大连接数
    pub max_connections: u32,
    /// 最小连接数
    pub min_connections: u32,
    /// 空闲超时
    pub idle_timeout: Option<u64>,
    /// 语句超时
    pub statement_timeout: u64,
}
