//! 考试会话仓储实现
//!
//! 实现考试会话的数据访问逻辑

use async_trait::async_trait;
use sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, ColumnTrait, Set, QueryOrder, QuerySelect};
use tracing::info;

use crate::error::Result;
use crate::domain::exam::{ExamSession, ExamSessionRepository, ExamType, Subject, SessionStatus, ModuleType};
use crate::infrastructure::database::models::exam_session::{Entity as ExamSessionEntity, ActiveModel, Model, Column};

/// 考试会话仓储实现
pub struct ExamSessionRepositoryImpl {
    db: DatabaseConnection,
}

impl ExamSessionRepositoryImpl {
    /// 创建新的考试会话仓储实例
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }

    /// 将数据库模型转换为领域实体
    fn model_to_domain(&self, model: Model) -> Result<ExamSession> {
        let exam_type = match model.exam_type.as_str() {
            "full" => ExamType::Full,
            "math" => ExamType::Math,
            "reading" => ExamType::Reading,
            _ => ExamType::Full,
        };

        let current_subject = match model.current_subject.as_deref().unwrap_or("reading") {
            "reading" => Subject::Reading,
            "math" => Subject::Math,
            _ => Subject::Reading,
        };

        let session_status = match model.session_status.as_str() {
            "in_progress" => SessionStatus::InProgress,
            "completed" => SessionStatus::Completed,
            _ => SessionStatus::InProgress,
        };

        let current_module_type = model.current_module_type
            .as_deref()
            .and_then(|s| ModuleType::from_str(s));

        let session = ExamSession {
            session_id: model.session_id,
            user_id: model.user_id,
            paper_id: model.paper_id,
            exam_type,
            current_subject,
            current_module_type,
            session_status,
            total_time_seconds: model.total_time_seconds.unwrap_or(0),
            reading_time_seconds: model.reading_time_seconds.unwrap_or(0),
            math_time_seconds: model.math_time_seconds.unwrap_or(0),
            created_at: model.created_at.into(),
            updated_at: model.updated_at.into(),
            completed_at: model.completed_at.map(|dt| dt.into()),
        };

        Ok(session)
    }
}

#[async_trait]
impl ExamSessionRepository for ExamSessionRepositoryImpl {
    async fn create(&self, session: &ExamSession) -> Result<()> {
        info!("创建考试会话记录: {}", session.session_id);

        let active_model = ActiveModel {
            session_id: Set(session.session_id.clone()),
            user_id: Set(session.user_id),
            paper_id: Set(session.paper_id),
            exam_type: Set(session.exam_type.to_string()),
            current_subject: Set(Some(session.current_subject.to_string())),
            current_module_type: Set(session.current_module_type.as_ref().map(|m| m.to_string())),
            session_status: Set(session.session_status.to_string()),
            break_start_time: Set(None),
            break_end_time: Set(None),
            total_time_seconds: Set(Some(session.total_time_seconds)),
            reading_time_seconds: Set(Some(session.reading_time_seconds)),
            math_time_seconds: Set(Some(session.math_time_seconds)),
            created_at: Set(session.created_at.into()),
            updated_at: Set(session.updated_at.into()),
            completed_at: Set(session.completed_at.map(|dt| dt.into())),
        };

        ExamSessionEntity::insert(active_model)
            .exec(&self.db)
            .await?;

        info!("成功创建考试会话记录: {}", session.session_id);
        Ok(())
    }

    async fn find_by_session_id(&self, session_id: &str) -> Result<Option<ExamSession>> {
        info!("查找考试会话: {}", session_id);

        let model = ExamSessionEntity::find_by_id(session_id.to_string())
            .one(&self.db)
            .await?;

        if let Some(model) = model {
            let session = self.model_to_domain(model)?;
            info!("找到考试会话: {}", session_id);
            Ok(Some(session))
        } else {
            info!("未找到考试会话: {}", session_id);
            Ok(None)
        }
    }

    async fn find_active_by_user_id(&self, user_id: i64) -> Result<Option<ExamSession>> {
        info!("查找用户进行中的考试会话: {}", user_id);

        let model = ExamSessionEntity::find()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::SessionStatus.eq("in_progress"))
            .order_by_desc(Column::CreatedAt)
            .one(&self.db)
            .await?;

        if let Some(model) = model {
            let session = self.model_to_domain(model)?;
            info!("找到用户进行中的考试会话: {}", session.session_id);
            Ok(Some(session))
        } else {
            info!("用户 {} 没有进行中的考试会话", user_id);
            Ok(None)
        }
    }

    async fn update(&self, session: &ExamSession) -> Result<()> {
        info!("更新考试会话: {}", session.session_id);

        let active_model = ActiveModel {
            session_id: Set(session.session_id.clone()),
            current_subject: Set(Some(session.current_subject.to_string())),
            current_module_type: Set(session.current_module_type.as_ref().map(|m| m.to_string())),
            session_status: Set(session.session_status.to_string()),
            total_time_seconds: Set(Some(session.total_time_seconds)),
            reading_time_seconds: Set(Some(session.reading_time_seconds)),
            math_time_seconds: Set(Some(session.math_time_seconds)),
            updated_at: Set(session.updated_at.into()),
            completed_at: Set(session.completed_at.map(|dt| dt.into())),
            ..Default::default()
        };

        ExamSessionEntity::update(active_model)
            .exec(&self.db)
            .await?;

        info!("成功更新考试会话: {}", session.session_id);
        Ok(())
    }

    async fn find_history_by_user_id(&self, user_id: i64, limit: Option<i32>) -> Result<Vec<ExamSession>> {
        info!("查找用户历史考试会话: {}, 限制: {:?}", user_id, limit);

        let limit_value = limit.unwrap_or(50) as u64;

        let models = ExamSessionEntity::find()
            .filter(Column::UserId.eq(user_id))
            .order_by_desc(Column::CreatedAt)
            .limit(limit_value)
            .all(&self.db)
            .await?;

        let mut sessions = Vec::new();
        for model in models {
            let session = self.model_to_domain(model)?;
            sessions.push(session);
        }

        info!("找到 {} 条历史考试会话记录", sessions.len());
        Ok(sessions)
    }
}