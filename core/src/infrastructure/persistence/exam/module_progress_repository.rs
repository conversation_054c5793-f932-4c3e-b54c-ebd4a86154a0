//! 考试模块进度仓储实现
//!
//! 实现考试模块进度的数据访问逻辑

use async_trait::async_trait;
use sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, ColumnTrait, Set, QueryOrder, ActiveModelTrait};
use tracing::{info, error};
use chrono::Utc;

use crate::error::Result;
use crate::domain::exam::{ExamModuleProgress, ExamModuleProgressRepository, ModuleType, Subject};
use crate::infrastructure::database::models::exam_module_progress::{Entity as ExamModuleProgressEntity, Model as ExamModuleProgressModel, ActiveModel as ExamModuleProgressActiveModel};

/// 考试模块进度仓储实现
pub struct ExamModuleProgressRepositoryImpl {
    db: DatabaseConnection,
}

impl ExamModuleProgressRepositoryImpl {
    /// 创建新的考试模块进度仓储实例
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl ExamModuleProgressRepository for ExamModuleProgressRepositoryImpl {
    async fn create(&self, progress: &ExamModuleProgress) -> Result<()> {
        info!("创建模块进度记录: 会话ID={}, 模块类型={}",
              progress.session_id, progress.module_type);

        // 为了避免唯一约束冲突，将学科信息包含在module_type中
        let module_type_with_subject = format!("{}_{}",
            progress.subject.to_string().to_lowercase(),
            progress.module_type.to_string());

        let active_model = ExamModuleProgressActiveModel {
            session_id: Set(progress.session_id.clone()),
            user_id: Set(progress.user_id),
            paper_id: Set(progress.paper_id),
            module_type: Set(module_type_with_subject),
            subject: Set(progress.subject.to_string()),
            total_questions: Set(progress.total_questions),
            answered_questions: Set(Some(progress.answered_questions)),
            correct_questions: Set(Some(progress.correct_questions)),
            time_limit_seconds: Set(progress.time_limit_seconds),
            time_used_seconds: Set(Some(progress.time_used_seconds)),
            remaining_time_seconds: Set(progress.remaining_time_seconds),
            module_status: Set(progress.module_status.to_string()),
            started_at: Set(progress.started_at.map(|dt| dt.into())),
            completed_at: Set(progress.completed_at.map(|dt| dt.into())),
            created_at: Set(Utc::now().into()),
            updated_at: Set(Utc::now().into()),
            ..Default::default()
        };

        match ExamModuleProgressEntity::insert(active_model).exec(&self.db).await {
            Ok(_) => {
                info!("成功创建模块进度记录: 会话ID={}", progress.session_id);
                Ok(())
            }
            Err(e) => {
                error!("创建模块进度记录失败: {:?}", e);
                Err(crate::error::Error::database(format!("创建模块进度记录失败: {}", e)))
            }
        }
    }

    async fn batch_create(&self, progresses: &[ExamModuleProgress]) -> Result<()> {
        info!("批量创建模块进度记录: 数量={}", progresses.len());

        for progress in progresses {
            self.create(progress).await?;
        }

        info!("成功批量创建模块进度记录: 数量={}", progresses.len());
        Ok(())
    }

    async fn update(&self, progress: &ExamModuleProgress) -> Result<()> {
        info!("更新模块进度记录: 会话ID={}, 模块类型={}",
              progress.session_id, progress.module_type);

        // 构建模块类型字符串（包含学科信息）
        let module_type_with_subject = format!("{}_{}",
            progress.subject.to_string().to_lowercase(),
            progress.module_type.to_string()
        );

        let active_model = ExamModuleProgressActiveModel {
            id: Set(progress.id.unwrap_or(0)),
            session_id: Set(progress.session_id.clone()),
            user_id: Set(progress.user_id),
            paper_id: Set(progress.paper_id),
            module_type: Set(module_type_with_subject),
            subject: Set(progress.subject.to_string()),
            total_questions: Set(progress.total_questions),
            answered_questions: Set(Some(progress.answered_questions)),
            correct_questions: Set(Some(progress.correct_questions)),
            time_limit_seconds: Set(progress.time_limit_seconds),
            time_used_seconds: Set(Some(progress.time_used_seconds)),
            remaining_time_seconds: Set(progress.remaining_time_seconds),
            module_status: Set(progress.module_status.to_string()),
            started_at: Set(progress.started_at.map(|dt| dt.into())),
            completed_at: Set(progress.completed_at.map(|dt| dt.into())),
            created_at: Set(progress.created_at.into()),
            updated_at: Set(progress.updated_at.into()),
        };

        match active_model.update(&self.db).await {
            Ok(_) => {
                info!("成功更新模块进度记录: 会话ID={}", progress.session_id);
                Ok(())
            }
            Err(e) => {
                error!("更新模块进度记录失败: {:?}", e);
                Err(crate::error::Error::database(format!("更新模块进度记录失败: {}", e)))
            }
        }
    }

    async fn find_by_session_id(&self, session_id: &str) -> Result<Vec<ExamModuleProgress>> {
        info!("查找会话的所有模块进度: 会话ID={}", session_id);

        let models = ExamModuleProgressEntity::find()
            .filter(crate::infrastructure::database::models::exam_module_progress::Column::SessionId.eq(session_id))
            .order_by_asc(crate::infrastructure::database::models::exam_module_progress::Column::ModuleType)
            .all(&self.db)
            .await
            .map_err(|e| crate::error::Error::database(format!("查询模块进度失败: {}", e)))?;

        let progresses: Vec<ExamModuleProgress> = models.into_iter()
            .map(|model| self.convert_model_to_domain(model))
            .collect();

        info!("找到 {} 条模块进度记录", progresses.len());
        Ok(progresses)
    }

    async fn find_by_session_and_module(
        &self,
        session_id: &str,
        module_type: ModuleType,
    ) -> Result<Option<ExamModuleProgress>> {
        info!("查找特定模块进度: 会话ID={}, 模块类型={}", session_id, module_type);

        // 注意：这个方法需要学科信息才能正确查询，但接口中没有学科参数
        // 暂时查询所有匹配的记录，然后在内存中过滤
        let models = ExamModuleProgressEntity::find()
            .filter(crate::infrastructure::database::models::exam_module_progress::Column::SessionId.eq(session_id))
            .all(&self.db)
            .await
            .map_err(|e| crate::error::Error::database(format!("查询模块进度失败: {}", e)))?;

        // 在内存中查找匹配的模块类型
        let model = models.into_iter()
            .find(|m| {
                // 从存储的module_type中提取原始模块类型
                if let Some((_subject, stored_module_type)) = m.module_type.split_once('_') {
                    stored_module_type == module_type.to_string()
                } else {
                    m.module_type == module_type.to_string()
                }
            });

        if let Some(model) = model {
            info!("找到模块进度记录");
            Ok(Some(self.convert_model_to_domain(model)))
        } else {
            info!("未找到模块进度记录");
            Ok(None)
        }
    }

    async fn find_by_session_and_subject(
        &self,
        session_id: &str,
        subject: Subject,
    ) -> Result<Vec<ExamModuleProgress>> {
        info!("查找学科的所有模块进度: 会话ID={}, 学科={}", session_id, subject);

        // TODO: 实现真正的数据库查询
        // 暂时返回空列表
        let progresses = vec![];

        info!("找到 {} 条学科模块进度记录", progresses.len());
        Ok(progresses)
    }
}

impl ExamModuleProgressRepositoryImpl {
    /// 将数据库模型转换为领域实体
    fn convert_model_to_domain(&self, model: ExamModuleProgressModel) -> ExamModuleProgress {
        // 从存储的module_type中提取原始模块类型
        let module_type = if let Some((_subject, stored_module_type)) = model.module_type.split_once('_') {
            ModuleType::from(stored_module_type.to_string())
        } else {
            ModuleType::from(model.module_type)
        };

        ExamModuleProgress {
            id: Some(model.id),
            session_id: model.session_id,
            user_id: model.user_id,
            paper_id: model.paper_id,
            module_type,
            subject: Subject::from(model.subject),
            total_questions: model.total_questions,
            answered_questions: model.answered_questions.unwrap_or(0),
            correct_questions: model.correct_questions.unwrap_or(0),
            time_limit_seconds: model.time_limit_seconds,
            time_used_seconds: model.time_used_seconds.unwrap_or(0),
            remaining_time_seconds: model.remaining_time_seconds,
            module_status: crate::domain::exam::ModuleStatus::from(model.module_status),
            started_at: model.started_at.map(|dt| dt.with_timezone(&chrono::Utc)),
            completed_at: model.completed_at.map(|dt| dt.with_timezone(&chrono::Utc)),
            created_at: model.created_at.with_timezone(&chrono::Utc),
            updated_at: model.updated_at.with_timezone(&chrono::Utc),
        }
    }
}
