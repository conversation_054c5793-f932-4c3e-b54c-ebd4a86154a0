//! 考试模块依赖注入工厂
//!
//! 提供考试相关服务的创建和配置

use std::sync::Arc;
use sea_orm::DatabaseConnection;

use crate::error::Result;
use crate::application::exam::{ExamApplicationService, ExamApplicationServiceImpl};
use crate::domain::exam::{
    ExamSessionDomainService, ExamQuestionDomainService,
    ExamSessionRepository, ExamModuleProgressRepository, ExamAnswerRepository, SatPaperRepository,
};
use crate::infrastructure::persistence::exam::{
    ExamSessionRepositoryImpl, SatPaperRepositoryImpl,
    ExamModuleProgressRepositoryImpl, ExamAnswerRepositoryImpl,
};
use crate::application::question::service::QuestionApplicationService;

/// 创建考试会话仓储
pub fn create_exam_session_repository(db: DatabaseConnection) -> Arc<dyn ExamSessionRepository> {
    Arc::new(ExamSessionRepositoryImpl::new(db))
}

/// 创建考试应用服务
/// 
/// 这是一个简化版本，只实现了基本的会话管理功能
/// TODO: 完善其他仓储的实现
pub fn create_exam_application_service(
    db: DatabaseConnection,
    question_service: Arc<dyn QuestionApplicationService>,
) -> Result<Arc<dyn ExamApplicationService>> {
    // 1. 创建仓储实现
    let session_repository = create_exam_session_repository(db.clone());
    
    // 2. 创建其他仓储实现
    let paper_repository: Arc<dyn SatPaperRepository> =
        Arc::new(SatPaperRepositoryImpl::new(db.clone()));
    let progress_repository: Arc<dyn ExamModuleProgressRepository> =
        Arc::new(ExamModuleProgressRepositoryImpl::new(db.clone()));
    let answer_repository: Arc<dyn ExamAnswerRepository> =
        Arc::new(ExamAnswerRepositoryImpl::new(db.clone()));

    // 2. 创建领域服务
    let session_domain_service = Arc::new(ExamSessionDomainService::new(
        session_repository.clone(),
        progress_repository.clone(),
        paper_repository.clone(),
    ));

    let question_domain_service = Arc::new(ExamQuestionDomainService::new(
        paper_repository.clone(),
        answer_repository.clone(),
    ));

    // 3. 创建应用服务
    let app_service = Arc::new(ExamApplicationServiceImpl::new(
        session_domain_service,
        question_domain_service,
        session_repository,
        progress_repository,
        answer_repository,
        paper_repository,
        question_service,
    ));

    Ok(app_service)
}
