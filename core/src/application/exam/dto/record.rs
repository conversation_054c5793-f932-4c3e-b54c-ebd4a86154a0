//! 考试记录相关数据传输对象
//!
//! 定义考试记录查询、统计等相关的DTO结构

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use super::common::PaginationDto;
use super::score::ExamScoreDto;
use super::statistics::ExamStatisticsDto;

/// 获取考试记录请求DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetExamRecordsRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID（可选，用于筛选特定试卷）
    pub paper_id: Option<i64>,
    /// 考试类型（可选，用于筛选特定类型）
    pub exam_type: Option<String>,
    /// 页码（从1开始）
    pub page: Option<u64>,
    /// 每页大小
    pub page_size: Option<u64>,
}

/// 获取指定试卷考试记录请求DTO
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GetPaperExamRecordsRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID列表
    pub paper_ids: Vec<i64>,
}

/// 考试记录响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamRecordsResponseDto {
    /// 考试记录统计列表
    pub paper_statistics: Vec<PaperStatisticsDto>,
    /// 分页信息
    pub pagination: PaginationDto,
}

/// 指定试卷考试记录响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperExamRecordsResponseDto {
    /// 考试记录统计列表（不分页）
    pub paper_statistics: Vec<PaperStatisticsDto>,
}

/// 试卷统计DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperStatisticsDto {
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: String,
    /// 考试次数
    pub exam_count: u32,
    /// 完成次数
    pub completed_count: u32,
    /// 最高分
    pub best_score: Option<u32>,
    /// 最新分数
    pub latest_score: Option<u32>,
    /// 平均分
    pub average_score: Option<f64>,
    /// 最近考试详情
    pub last_exam_detail: Option<LastExamDetailDto>,
    /// 进步趋势
    pub progress_trend: ProgressTrendDto,
}

/// 最近考试详情DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LastExamDetailDto {
    /// 会话ID
    pub session_id: String,
    /// 总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
    /// 考试进度
    pub exam_progress: Option<f64>,
    /// 考试状态
    pub exam_status: String,
}

/// 进步趋势DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressTrendDto {
    /// 是否在进步
    pub is_improving: bool,
    /// 分数变化
    pub score_change: i32,
    /// 趋势描述
    pub trend_description: String,
}

/// 提交考试请求DTO
#[derive(Debug, Deserialize)]
pub struct SubmitExamRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 是否强制提交（即使未完成所有题目）
    pub force_submit: Option<bool>,
}

/// 提交考试响应DTO
#[derive(Debug, Serialize)]
pub struct SubmitExamResponseDto {
    /// 提交是否成功
    pub success: bool,
    /// 会话ID
    pub session_id: String,
    /// 考试成绩
    pub exam_score: ExamScoreDto,
    /// 考试统计
    pub statistics: ExamStatisticsDto,
    /// 提交时间
    pub submitted_at: DateTime<Utc>,
    /// 考试总用时（秒）
    pub total_duration_seconds: i32,
    /// 考试状态
    pub exam_status: String,
}

/// 考试题目概览请求DTO
#[derive(Debug, Deserialize)]
pub struct GetExamOverviewRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
}

/// 考试题目概览响应DTO
#[derive(Debug, Serialize)]
pub struct ExamOverviewResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: String,
    /// 按模块分组的题目详情
    pub module_details: Vec<ModuleOverviewDto>,
}

/// 模块概览DTO
#[derive(Debug, Serialize)]
pub struct ModuleOverviewDto {
    /// 模块类型
    pub module_type: String,
    /// 学科
    pub subject: String,
    /// 该模块的题目状态列表
    pub questions: Vec<QuestionStatusDto>,
}

/// 题目状态DTO（轻量级，不包含题目内容）
#[derive(Debug, Serialize)]
pub struct QuestionStatusDto {
    /// 题目ID
    pub question_id: i32,
    /// 题目在模块中的序号
    pub module_sequence: i32,
    /// 答题状态：correct=答对, incorrect=答错, unanswered=未答, skipped=跳过
    pub answer_status: String,
    /// 用户答案
    pub user_answer: Option<String>,
    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,
}

/// 分模块答题详情请求DTO
#[derive(Debug, Deserialize)]
pub struct GetModuleDetailsRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 模块类型
    pub module_type: String,
}

/// 获取用户历史答卷记录列表请求DTO
#[derive(Debug, Deserialize)]
pub struct GetUserExamHistoryListRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 页码（从1开始，可选）
    pub page: Option<u64>,
    /// 每页大小（可选，默认20）
    pub page_size: Option<u64>,
}

/// 获取用户历史答卷记录列表响应DTO
#[derive(Debug, Serialize)]
pub struct UserExamHistoryListResponseDto {
    /// 历史记录列表
    pub exam_histories: Vec<ExamHistoryItemDto>,
    /// 分页信息
    pub pagination: super::common::PaginationDto,
}

/// 历史记录项DTO（列表项）
#[derive(Debug, Serialize)]
pub struct ExamHistoryItemDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: String,
    /// 考试状态
    pub exam_status: String,
    /// 考试总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
    /// 总题数
    pub total_questions: i32,
    /// 答对题数
    pub correct_count: i32,
    /// 答错题数
    pub incorrect_count: i32,
    /// 未答题数
    pub unanswered_count: i32,
}

/// 获取试卷题目概览请求DTO（第二层：显示所有题目的答题状态）
#[derive(Debug, Deserialize)]
pub struct GetExamQuestionsOverviewRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
}

/// 获取试卷题目概览响应DTO
#[derive(Debug, Serialize)]
pub struct ExamQuestionsOverviewResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: String,
    /// 考试基本信息
    pub exam_info: ExamBasicInfoDto,
    /// 按模块分组的题目状态
    pub modules: Vec<ModuleQuestionsOverviewDto>,
}

/// 考试基本信息DTO
#[derive(Debug, Serialize)]
pub struct ExamBasicInfoDto {
    /// 考试状态
    pub exam_status: String,
    /// 考试总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
}

/// 模块题目概览DTO
#[derive(Debug, Serialize)]
pub struct ModuleQuestionsOverviewDto {
    /// 模块类型
    pub module_type: String,
    /// 学科
    pub subject: String,
    /// 模块分数
    pub module_score: Option<u32>,
    /// 模块正确率
    pub module_accuracy: Option<f64>,
    /// 该模块的题目状态列表（1-22或1-27）
    pub questions: Vec<QuestionStatusOverviewDto>,
}

/// 题目状态概览DTO（不包含题目内容，只显示答题状态）
#[derive(Debug, Serialize)]
pub struct QuestionStatusOverviewDto {
    /// 题目ID
    pub question_id: i32,
    /// 题目在模块中的序号（1-22或1-27）
    pub module_sequence: i32,
    /// 答题状态：correct=答对, incorrect=答错, unanswered=未答, skipped=跳过
    pub answer_status: String,
    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,
}

/// 获取模块详细题目内容请求DTO（第三层：显示题目内容和答题情况）
#[derive(Debug, Deserialize)]
pub struct GetModuleDetailedQuestionsRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 模块类型
    pub module_type: String,
}

/// 获取模块详细题目内容响应DTO
#[derive(Debug, Serialize)]
pub struct ModuleDetailedQuestionsResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 模块类型
    pub module_type: String,
    /// 学科
    pub subject: String,
    /// 模块基本信息
    pub module_info: ModuleBasicInfoDto,
    /// 该模块的详细题目列表（包含题目内容）
    pub questions: Vec<DetailedQuestionDto>,
}

/// 模块基本信息DTO
#[derive(Debug, Serialize)]
pub struct ModuleBasicInfoDto {
    /// 模块分数
    pub module_score: Option<u32>,
    /// 模块正确率
    pub module_accuracy: Option<f64>,
    /// 模块用时（分钟）
    pub module_duration: Option<u32>,
    /// 模块状态
    pub module_status: String,
    /// 总题数
    pub total_questions: i32,
    /// 答对题数
    pub correct_count: i32,
    /// 答错题数
    pub incorrect_count: i32,
    /// 未答题数
    pub unanswered_count: i32,
}

/// 详细题目DTO（包含完整题目内容和答题情况）
#[derive(Debug, Serialize)]
pub struct DetailedQuestionDto {
    /// 题目ID
    pub question_id: i32,
    /// 题目在模块中的序号（1-22或1-27）
    pub module_sequence: i32,
    /// 题目内容
    pub question_content: String,
    /// 选项列表
    pub options: Vec<String>,
    /// 正确答案
    pub correct_answer: String,
    /// 用户答案
    pub user_answer: Option<String>,
    /// 答题状态：correct=答对, incorrect=答错, unanswered=未答, skipped=跳过
    pub answer_status: String,
    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,
    /// 题目难度（ELO评分）
    pub difficulty: Option<f64>,
    /// 知识点ID
    pub knowledge_point_id: Option<i32>,
    /// 知识点名称
    pub knowledge_point_name: Option<String>,
    /// 题目解析（可选）
    pub explanation: Option<String>,
}

/// 分模块答题详情响应DTO
#[derive(Debug, Serialize)]
pub struct ModuleDetailsResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 模块类型
    pub module_type: String,
    /// 学科
    pub subject: String,
    /// 题目数量
    pub total_questions: i32,
    /// 已答题数
    pub answered_questions: i32,
    /// 题目详情列表
    pub questions: Vec<QuestionWithAnswerDetailDto>,
}

/// 题目详情DTO（包含完整题目内容和答题详情）
#[derive(Debug, Serialize)]
pub struct QuestionWithAnswerDetailDto {
    /// 基础题目内容（使用现有的ApiQuestionContent格式）
    #[serde(flatten)]
    pub question: crate::infrastructure::dto::question::ApiQuestionContent,
    /// 模块内顺序（考试特有字段）
    pub module_sequence: i32,
    /// 用户答案
    pub user_answer: Option<String>,
    /// 是否正确
    pub is_correct: Option<bool>,
    /// 答题状态
    pub answer_status: String,
    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,
    /// 答题时间
    pub answered_at: Option<DateTime<Utc>>,
}

/// 考试结构请求DTO
#[derive(Debug, Deserialize)]
pub struct GetExamStructureRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
}

/// 考试结构响应DTO
#[derive(Debug, Serialize)]
pub struct ExamStructureResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: String,
    /// 模块结构列表
    pub modules: Vec<ModuleStructureDto>,
}

/// 模块结构DTO
#[derive(Debug, Serialize)]
pub struct ModuleStructureDto {
    /// 模块类型
    pub module_type: String,
    /// 学科
    pub subject: String,
    /// 题目数量
    pub question_count: i32,
    /// 题目序号范围
    pub question_range: String, // 例如: "1-22"
    /// 题目ID列表（按模块内顺序）
    pub question_ids: Vec<i32>,
}

/// 分模块答题详情请求DTO
#[derive(Debug, Deserialize)]
pub struct GetModuleAnswerDetailsRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 模块类型
    pub module_type: String,
    /// 学科
    pub subject: String,
}

/// 分模块答题详情响应DTO
#[derive(Debug, Serialize)]
pub struct ModuleAnswerDetailsResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 模块类型
    pub module_type: String,
    /// 学科
    pub subject: String,
    /// 题目数量
    pub total_questions: i32,
    /// 已答题数
    pub answered_questions: i32,
    /// 题目和答题详情列表
    pub question_details: Vec<QuestionAnswerDetailDto>,
}

/// 题目答题详情DTO
#[derive(Debug, Serialize)]
pub struct QuestionAnswerDetailDto {
    /// 题目在模块中的序号
    pub module_sequence: i32,
    /// 题目ID
    pub question_id: i32,
    /// 题目内容
    pub question_content: serde_json::Value,
    /// 选项
    pub options: Option<serde_json::Value>,
    /// 正确答案
    pub correct_answer: Option<String>,
    /// 用户答案
    pub user_answer: Option<String>,
    /// 是否正确
    pub is_correct: Option<bool>,
    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,
    /// 答题状态
    pub answer_status: String, // answered, unanswered, skipped
    /// 答题时间
    pub answered_at: Option<DateTime<Utc>>,
}

impl ExamOverviewResponseDto {
    /// 从领域数据转换为DTO
    pub fn from_domain(data: crate::domain::exam::services::ExamOverviewData) -> Self {
        // 按模块分组题目
        let mut module_map: std::collections::BTreeMap<String, Vec<QuestionStatusDto>> = std::collections::BTreeMap::new();
        let mut subject_map: std::collections::HashMap<String, String> = std::collections::HashMap::new();

        // 构建答题记录映射
        let mut answer_map = std::collections::HashMap::new();
        for answer in &data.answers {
            answer_map.insert(answer.question_id, answer);
        }

        for question in &data.questions {
            let module_type_str = question.get_module_type()
                .map(|mt| mt.to_string())
                .unwrap_or_else(|| "unknown".to_string());

            let subject_str = match question.get_subject() {
                crate::domain::exam::value_objects::Subject::Math => "math".to_string(),
                crate::domain::exam::value_objects::Subject::Reading => "reading".to_string(),
            };

            // 记录模块对应的学科
            subject_map.insert(module_type_str.clone(), subject_str);

            // 获取答题状态
            let answer_status = if let Some(answer) = answer_map.get(&question.question_id) {
                match (answer.user_answer.as_ref(), answer.is_correct) {
                    (Some(_), Some(true)) => "correct".to_string(),
                    (Some(_), Some(false)) => "incorrect".to_string(),
                    (Some(_), None) => "answered".to_string(),
                    (None, _) => "unanswered".to_string(),
                }
            } else {
                "unanswered".to_string()
            };

            let question_status = QuestionStatusDto {
                question_id: question.question_id,
                module_sequence: question.module_sequence,
                answer_status,
                user_answer: answer_map.get(&question.question_id)
                    .and_then(|a| a.user_answer.clone()),
                response_time_seconds: answer_map.get(&question.question_id)
                    .and_then(|a| a.response_time_seconds),
            };

            module_map.entry(module_type_str).or_insert_with(Vec::new).push(question_status);
        }

        // 构建模块详情列表
        let mut module_details = Vec::new();
        for (module_type, questions) in module_map {
            let subject = subject_map.get(&module_type).cloned().unwrap_or("unknown".to_string());

            module_details.push(ModuleOverviewDto {
                module_type,
                subject,
                questions,
            });
        }

        Self {
            session_id: data.session_id,
            paper_id: data.paper_id,
            paper_name: data.paper_name,
            exam_type: data.exam_type,
            module_details,
        }
    }
}

impl ModuleDetailsResponseDto {
    /// 从领域数据转换为DTO
    pub fn from_domain(data: crate::domain::exam::services::ModuleDetailsData) -> Self {
        // 构建答题记录映射
        let mut answer_map = std::collections::HashMap::new();
        for answer in &data.answers {
            answer_map.insert(answer.question_id, answer);
        }

        let total_questions = data.questions.len() as i32;
        let answered_questions = data.answers.iter()
            .filter(|a| a.user_answer.is_some())
            .count() as i32;

        let subject_str = match data.subject {
            crate::domain::exam::value_objects::Subject::Math => "math".to_string(),
            crate::domain::exam::value_objects::Subject::Reading => "reading".to_string(),
        };

        // TODO: 这里需要查询完整的题目内容
        // 暂时返回空的questions列表，需要在基础设施层实现题目内容查询
        let questions = Vec::new();

        Self {
            session_id: data.session_id,
            module_type: data.module_type.to_string(),
            subject: subject_str,
            total_questions,
            answered_questions,
            questions,
        }
    }
}
