//! 考试题目相关数据传输对象
//!
//! 定义考试题目、内容等相关的DTO结构

use serde::{Deserialize, Serialize};

/// 考试题目DTO - 组合ApiQuestionContent和exam特有字段
#[derive(Debug, Serialize)]
pub struct ExamQuestionDto {
    /// 基础题目内容（使用推荐系统的统一格式）
    #[serde(flatten)]
    pub question: crate::infrastructure::dto::question::ApiQuestionContent,
    /// 模块内顺序（考试特有字段）
    pub module_sequence: i32,
}

impl ExamQuestionDto {
    /// 从 QuestionContentDetail 创建考试题目DTO
    pub fn from_question_detail(
        detail: &crate::domain::exam::repository::QuestionContentDetail,
        module_sequence: i32,
        include_answers: bool,
    ) -> Self {
        // 使用core模块的ApiQuestionContent
        let api_question = crate::infrastructure::dto::question::ApiQuestionContent::from_question_content(
            crate::infrastructure::dto::question::QuestionContent {
                id: detail.question_id.to_string(),
                subject_id: detail.subject_id,
                knowledge_id: detail.knowledge_id,
                type_id: detail.type_id,
                difficulty: detail.difficulty as f64,
                question_content: detail.question_content.clone(),
                options: detail.options.clone().unwrap_or(serde_json::Value::Array(vec![])),
                answer: if include_answers { detail.answer.clone() } else { serde_json::Value::Null },
                explanation: if include_answers {
                    detail.explanation.clone().unwrap_or(serde_json::Value::Array(vec![]))
                } else {
                    serde_json::Value::Array(vec![])
                },
                elo_rating: detail.elo_rating,
                usage_count: detail.usage_count,
                correct_count: detail.correct_count,
                question_set: detail.question_set.clone(),
                url: detail.url.clone(),
                is_active: detail.is_active,
                created_at: detail.created_at,
                updated_at: detail.updated_at,
                section_id: detail.section_id,
                metadata: None,
            }
        );

        Self {
            question: api_question,
            module_sequence,
        }
    }
}

/// 获取试卷题目请求DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetPaperQuestionsRequestDto {
    /// 试卷ID
    pub paper_id: i64,
    /// 是否包含答案（默认false，考试时不返回答案）
    pub include_answers: Option<bool>,
    /// 模块类型过滤（可选）
    pub module_type: Option<String>,
    /// 学科过滤（可选）
    pub subject_id: Option<i32>,
}

/// 获取试卷题目响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetPaperQuestionsResponseDto {
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 试卷版本
    pub version: i32,
    /// 题目列表
    pub questions: Vec<ExamQuestionContentDto>,
    /// 总题目数
    pub total_count: i32,
    /// 按学科分组的统计
    pub subject_stats: Vec<SubjectStatDto>,
}

/// 考试题目内容DTO（扩展版本）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamQuestionContentDto {
    /// 题目ID
    pub id: i32,
    /// 学科ID
    pub subject_id: i32,
    /// 知识点ID
    pub knowledge_id: i32,
    /// 题型ID
    pub type_id: i32,
    /// 题目内容
    pub content: serde_json::Value,
    /// 选项
    pub options: Option<serde_json::Value>,
    /// 答案（考试时不返回）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub answer: Option<serde_json::Value>,
    /// 解析（考试时不返回）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub explanation: Option<serde_json::Value>,
    /// ELO评分
    pub elo_rating: f64,
    /// 模块内顺序
    pub module_sequence: i32,
    /// 模块类型
    pub module_type: String,
    /// 难度等级
    pub difficulty: i16,
    /// 是否激活
    pub is_active: bool,
}

/// 学科统计DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubjectStatDto {
    /// 学科ID
    pub subject_id: i32,
    /// 学科名称
    pub subject_name: String,
    /// 题目数量
    pub question_count: i32,
    /// 模块统计
    pub module_stats: Vec<ModuleStatDto>,
}

/// 模块统计DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleStatDto {
    /// 模块类型
    pub module_type: String,
    /// 题目数量
    pub question_count: i32,
}
