//! 考试应用服务通用数据传输对象
//!
//! 定义考试系统中通用的DTO结构

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::domain::exam::value_objects::{ModuleType, Subject, ModuleStatus};

/// 通用错误响应DTO
#[derive(Debug, Serialize)]
pub struct ErrorResponseDto {
    /// 错误码
    pub error_code: String,
    /// 错误消息
    pub message: String,
    /// 详细信息
    pub details: Option<serde_json::Value>,
}

/// 分页信息DTO - 使用系统统一的分页结构
pub type PaginationDto = crate::models::Pagination;

/// 学科信息DTO
#[derive(Debug, Serialize)]
pub struct SectionInfoDto {
    /// 学科名称
    pub section_name: String,
    /// 学科
    pub subject: Subject,
    /// 模块列表
    pub modules: Vec<ModuleInfoDto>,
}

/// 模块信息DTO
#[derive(Debug, Serialize)]
pub struct ModuleInfoDto {
    /// 模块类型
    pub module_type: ModuleType,
    /// 题目数量
    pub question_count: i32,
    /// 时间限制（分钟）
    pub time_limit_minutes: i32,
    /// 难度级别
    pub difficulty_level: String,
    /// 模块状态
    pub status: ModuleStatus,
    /// 自适应信息（仅对第二模块有效）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub adaptive_info: Option<AdaptiveModuleInfoDto>,
}

/// 自适应模块信息DTO
#[derive(Debug, Serialize)]
pub struct AdaptiveModuleInfoDto {
    /// 依赖的模块类型
    pub depends_on: ModuleType,
    /// 可能的模块类型
    pub possible_types: Vec<ModuleType>,
    /// 实际确定的模块类型（如果已确定）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub actual_type: Option<ModuleType>,
    /// 表现分数（如果已计算）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub performance_score: Option<f32>,
}

/// 时间信息DTO
#[derive(Debug, Serialize)]
pub struct TimeInfoDto {
    /// 考试开始时间
    pub exam_start_time: DateTime<Utc>,
    /// 当前时间
    pub current_time: DateTime<Utc>,
    /// 已用时间（秒）
    pub elapsed_seconds: i32,
    /// 剩余时间（秒）
    pub remaining_seconds: i32,
    /// 是否接近超时
    pub is_near_timeout: bool,
    /// 超时警告阈值（秒）
    pub timeout_warning_threshold: i32,
}

/// 推荐操作DTO
#[derive(Debug, Serialize)]
pub struct RecommendationDto {
    /// 推荐类型
    pub recommendation_type: RecommendationType,
    /// 推荐标题
    pub title: String,
    /// 推荐描述
    pub description: String,
    /// 优先级（1-5，5最高）
    pub priority: i32,
    /// 相关参数
    pub parameters: Option<serde_json::Value>,
}

/// 推荐类型
#[derive(Debug, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum RecommendationType {
    /// 时间管理
    TimeManagement,
    /// 答题策略
    AnsweringStrategy,
    /// 模块切换
    ModuleTransition,
    /// 休息建议
    BreakSuggestion,
    /// 完成提醒
    CompletionReminder,
}

/// 下一步操作DTO
#[derive(Debug, Clone, Serialize)]
pub struct NextActionDto {
    /// 操作类型
    pub action_type: NextActionType,
    /// 操作描述
    pub description: String,
    /// 是否可以继续
    pub can_continue: bool,
    /// 建议等待时间（秒，用于休息时间）
    pub suggested_wait_seconds: Option<i32>,
}

/// 下一步操作类型
#[derive(Debug, Clone, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum NextActionType {
    /// 开始第二模块
    StartModule2,
    /// 切换学科
    SwitchSubject,
    /// 休息时间
    TakeBreak,
    /// 完成考试
    CompleteExam,
    /// 等待评分
    WaitForScoring,
}

/// 建议操作DTO
#[derive(Debug, Serialize)]
pub struct SuggestedActionDto {
    /// 操作类型
    pub action_type: SuggestedActionType,
    /// 操作描述
    pub description: String,
    /// 是否紧急
    pub is_urgent: bool,
    /// 相关参数
    pub parameters: Option<serde_json::Value>,
}

/// 建议操作类型
#[derive(Debug, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum SuggestedActionType {
    /// 继续答题
    ContinueAnswering,
    /// 提交模块
    SubmitModule,
    /// 切换模块
    SwitchModule,
    /// 休息一下
    TakeBreak,
    /// 完成考试
    CompleteExam,
}

/// 准备操作类型
#[derive(Debug, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum PrepareActionType {
    /// 创建新会话
    CreateNew,
    /// 恢复现有会话
    ResumeExisting,
    /// 取消旧会话并创建新会话
    CancelAndCreateNew,
}

/// 模块切换操作类型
#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum ModuleTransitionAction {
    /// 仅提交当前模块
    SubmitOnly,
    /// 仅开始下一模块
    StartNext,
    /// 提交当前模块并开始下一模块
    SubmitAndNext,
}
