//! 考试会话相关数据传输对象
//!
//! 定义考试会话创建、恢复、管理等相关的DTO结构

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::domain::exam::value_objects::{ExamType, ModuleType, Subject, SessionStatus};
use super::common::{SectionInfoDto, NextActionDto};
use super::module::{ModuleProgressDto, ExamStateDto};

/// 创建考试会话请求DTO
#[derive(Debug, Deserialize, Serialize)]
pub struct CreateExamSessionRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
}

/// 创建考试会话响应DTO
#[derive(Debug, Serialize)]
pub struct CreateExamSessionResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 总题数
    pub total_questions: i32,
    /// 总时长（分钟）
    pub total_time_minutes: i32,
    /// 学科信息
    pub sections: Vec<SectionInfoDto>,
}

/// 恢复考试会话请求DTO
#[derive(Debug, Deserialize)]
pub struct ResumeExamSessionRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
}

/// 恢复考试会话响应DTO
#[derive(Debug, Serialize)]
pub struct ResumeExamSessionResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 考试状态
    pub exam_state: ExamStateDto,
}

/// 考试会话DTO
#[derive(Debug, Serialize)]
pub struct ExamSessionDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
    /// 当前学科
    pub current_subject: Subject,
    /// 当前模块类型
    pub current_module_type: Option<ModuleType>,
    /// 会话状态
    pub session_status: SessionStatus,
    /// 总用时（秒）
    pub total_time_seconds: i32,
    /// 语言部分用时（秒）
    pub reading_time_seconds: i32,
    /// 数学部分用时（秒）
    pub math_time_seconds: i32,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
}

/// 考试准备请求DTO
#[derive(Debug, Deserialize)]
pub struct ExamPrepareRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
    /// 是否自动恢复未完成会话
    pub auto_resume: Option<bool>,
    /// 恢复的会话ID（可选，如果指定则尝试恢复该会话）
    pub resume_session_id: Option<String>,
    /// 是否直接开始第一模块
    pub auto_start_first_module: Option<bool>,
    /// 是否包含用户历史记录
    pub include_user_history: Option<bool>,
}

/// 会话信息DTO
#[derive(Debug, Serialize)]
pub struct SessionInfoDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 会话状态
    pub session_status: SessionStatus,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 总题数
    pub total_questions: i32,
    /// 总时长（分钟）
    pub total_time_minutes: i32,
}

/// 未完成会话DTO
#[derive(Debug, Serialize)]
pub struct IncompleteSessionDto {
    /// 会话ID
    pub session_id: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 当前学科
    pub current_subject: Subject,
    /// 当前模块类型
    pub current_module_type: Option<ModuleType>,
    /// 会话状态
    pub session_status: SessionStatus,
    /// 进度信息
    pub progress: SessionProgressDto,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后活动时间
    pub last_activity: DateTime<Utc>,
}

/// 会话进度DTO
#[derive(Debug, Serialize)]
pub struct SessionProgressDto {
    /// 已完成的模块数
    pub completed_modules: i32,
    /// 总模块数
    pub total_modules: i32,
    /// 已答题数
    pub answered_questions: i32,
    /// 总题数
    pub total_questions: i32,
    /// 完成百分比
    pub completion_percentage: f64,
}

/// 模块切换请求DTO
#[derive(Debug, Deserialize)]
pub struct ModuleTransitionRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 操作类型
    pub action: super::common::ModuleTransitionAction,
    /// 当前模块类型（如果需要提交）
    pub current_module_type: Option<ModuleType>,
    /// 下一模块类型（如果需要开始）
    pub next_module_type: Option<ModuleType>,
    /// 是否强制提交当前模块
    pub force_submit: Option<bool>,
}

/// 模块切换响应DTO
#[derive(Debug, Serialize)]
pub struct ModuleTransitionResponseDto {
    /// 操作成功
    pub success: bool,
    /// 执行的操作
    pub executed_actions: Vec<String>,
    /// 提交的模块结果（如果有提交操作）
    pub submit_result: Option<super::module::SubmitModuleResponseDto>,
    /// 开始的模块信息（如果有开始操作）
    pub start_result: Option<super::module::StartModuleResponseDto>,
    /// 完整考试状态
    pub exam_state: ExamStateDto,
    /// 下一步建议操作
    pub next_action: NextActionDto,
    /// 操作消息
    pub message: String,
}
