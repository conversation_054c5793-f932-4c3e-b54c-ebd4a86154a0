//! 考试记录服务工厂
//!
//! 提供正确的DDD依赖注入示例，展示如何在组合根中组装服务

use std::sync::Arc;
use sea_orm::DatabaseConnection;

use crate::application::exam::service_impl::ExamRecordServiceImpl;
use crate::domain::exam::repository::{
    ExamSessionRepository, ExamAnswerRepository, SatPaperRepository
};
use crate::infrastructure::persistence::exam::{
    ExamSessionRepositoryImpl, ExamAnswerRepositoryImpl, SatPaperRepositoryImpl
};

/// 考试记录服务工厂
/// 
/// 这是正确的DDD依赖注入方式：
/// 1. 在组合根中手动组装依赖关系
/// 2. 不依赖StorageManager等基础设施组件
/// 3. 遵循依赖倒置原则
pub struct ExamRecordServiceFactory;

impl ExamRecordServiceFactory {
    /// 创建考试记录应用服务
    /// 
    /// 这是正确的DDD组装方式：
    /// 1. 创建基础设施层的仓储实现
    /// 2. 将实现转换为领域接口
    /// 3. 注入到应用服务中
    pub fn create_service(db: DatabaseConnection) -> Arc<ExamRecordServiceImpl> {
        // 1. 创建基础设施层仓储实现
        let session_repository_impl = ExamSessionRepositoryImpl::new(db.clone());
        let answer_repository_impl = ExamAnswerRepositoryImpl::new(db.clone());
        let paper_repository_impl = SatPaperRepositoryImpl::new(db);

        // 2. 转换为领域接口（依赖倒置）
        let session_repository: Arc<dyn ExamSessionRepository> = Arc::new(session_repository_impl);
        let answer_repository: Arc<dyn ExamAnswerRepository> = Arc::new(answer_repository_impl);
        let paper_repository: Arc<dyn SatPaperRepository> = Arc::new(paper_repository_impl);

        // 3. 创建应用服务（依赖注入）
        Arc::new(ExamRecordServiceImpl::new(
            session_repository,
            answer_repository,
            paper_repository,
        ))
    }

    /// 创建考试记录应用服务（使用现有仓储实例）
    /// 
    /// 当你已经有仓储实例时使用此方法
    pub fn create_service_with_repositories(
        session_repository: Arc<dyn ExamSessionRepository>,
        answer_repository: Arc<dyn ExamAnswerRepository>,
        paper_repository: Arc<dyn SatPaperRepository>,
    ) -> Arc<ExamRecordServiceImpl> {
        Arc::new(ExamRecordServiceImpl::new(
            session_repository,
            answer_repository,
            paper_repository,
        ))
    }
}

/// 使用示例
/// 
/// ```rust
/// // 在你的应用程序启动代码中（组合根）：
/// 
/// use sea_orm::DatabaseConnection;
/// use crate::application::exam::service_impl::ExamRecordServiceFactory;
/// 
/// async fn setup_services(db: DatabaseConnection) {
///     // 正确的DDD方式：在组合根中手动组装
///     let exam_record_service = ExamRecordServiceFactory::create_service(db);
///     
///     // 使用服务
///     let request = GetExamOverviewRequestDto {
///         session_id: "exam_123".to_string(),
///         user_id: 456,
///     };
///     
///     let response = exam_record_service.get_exam_overview(request).await?;
/// }
/// ```
/// 
/// ## DDD架构原则
/// 
/// 1. **依赖方向**: 应用层 → 领域层 ← 基础设施层
/// 2. **依赖倒置**: 应用层依赖领域接口，不依赖具体实现
/// 3. **组合根**: 在应用程序入口处手动组装所有依赖
/// 4. **单一职责**: 每个仓储只负责一个聚合根的数据访问
/// 5. **接口隔离**: 每个服务只依赖它需要的接口
