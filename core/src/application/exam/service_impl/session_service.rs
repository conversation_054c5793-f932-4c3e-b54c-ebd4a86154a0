//! 考试会话服务实现
//!
//! 负责处理考试会话的创建、恢复、状态管理等功能

use std::sync::Arc;
use tracing::{info, warn};

use crate::error::Result;
use crate::domain::exam::{
    ExamSession, ExamSessionDomainService, ExamSessionRepository, SatPaperRepository,
    ExamType, Subject, SessionStatus, ModuleType, ModuleStatus, AdaptiveModuleService,
};

use super::super::dto::{
    CreateExamSessionRequestDto, CreateExamSessionResponseDto,
    ResumeExamSessionRequestDto, ResumeExamSessionResponseDto,
    ExamSessionDto, ExamStateDto, SectionInfoDto, ModuleInfoDto,
    ModuleProgressDto, AnswerSummaryDto, AdaptiveModuleInfoDto,
};

/// 会话服务实现
#[derive(Clone)]
pub struct SessionServiceImpl {
    session_domain_service: Arc<ExamSessionDomainService>,
    session_repository: Arc<dyn ExamSessionRepository>,
    paper_repository: Arc<dyn SatPaperRepository>,
    adaptive_service: Arc<AdaptiveModuleService>,
}

impl SessionServiceImpl {
    pub fn new(
        session_domain_service: Arc<ExamSessionDomainService>,
        session_repository: Arc<dyn ExamSessionRepository>,
        paper_repository: Arc<dyn SatPaperRepository>,
        adaptive_service: Arc<AdaptiveModuleService>,
    ) -> Self {
        Self {
            session_domain_service,
            session_repository,
            paper_repository,
            adaptive_service,
        }
    }

    /// 创建考试会话
    pub async fn create_session(
        &self,
        request: CreateExamSessionRequestDto,
    ) -> Result<CreateExamSessionResponseDto> {
        info!("创建考试会话: 用户ID={}, 试卷ID={}, 考试类型={}", 
              request.user_id, request.paper_id, request.exam_type);

        // 1. 检查是否已有进行中的会话
        if let Some(existing_session) = self.session_repository
            .find_active_by_user_id(request.user_id)
            .await?
        {
            info!("发现用户 {} 已有进行中的会话: {}, 现有类型: {}, 请求类型: {}",
                  request.user_id, existing_session.session_id, existing_session.exam_type, request.exam_type);

            // 检查考试类型是否匹配
            if existing_session.exam_type == request.exam_type {
                // 考试类型相同，返回已存在的会话
                warn!("用户 {} 已有相同类型的进行中考试会话: {}", request.user_id, existing_session.session_id);
                return Ok(CreateExamSessionResponseDto {
                    session_id: existing_session.session_id.clone(),
                    paper_id: existing_session.paper_id,
                    paper_name: "SAT模考试卷".to_string(), // TODO: 从试卷获取真实名称
                    exam_type: existing_session.exam_type,
                    total_questions: self.calculate_total_questions(&existing_session.exam_type),
                    total_time_minutes: self.calculate_total_time_minutes(&existing_session.exam_type),
                    sections: self.build_section_info_list_with_session(&existing_session.exam_type, Some(&existing_session.session_id)).await?,
                });
            } else {
                // 考试类型不同，返回错误
                warn!("用户 {} 已有不同类型的进行中考试会话: {} (当前: {}, 请求: {})",
                      request.user_id, existing_session.session_id, existing_session.exam_type, request.exam_type);
                return Err(crate::error::Error::service(format!(
                    "用户已有进行中的{}考试会话，请先完成或放弃当前会话后再创建{}考试",
                    existing_session.exam_type, request.exam_type
                )));
            }
        }

        // 2. 验证试卷存在
        let paper = self.paper_repository
            .find_paper_by_id(request.paper_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("试卷不存在"))?;

        // 3. 使用领域服务创建会话
        let session = self.session_domain_service
            .create_session(request.user_id, request.paper_id, request.exam_type)
            .await?;

        // 4. 构建响应
        let response = CreateExamSessionResponseDto {
            session_id: session.session_id.clone(),
            paper_id: session.paper_id,
            paper_name: paper.paper_name,
            exam_type: session.exam_type,
            total_questions: self.calculate_total_questions(&session.exam_type),
            total_time_minutes: self.calculate_total_time_minutes(&session.exam_type),
            sections: self.build_section_info_list(&session.exam_type).await?,
        };

        info!("考试会话创建成功: {}", response.session_id);
        Ok(response)
    }

    /// 恢复考试会话
    pub async fn resume_session(
        &self,
        request: ResumeExamSessionRequestDto,
    ) -> Result<ResumeExamSessionResponseDto> {
        info!("恢复考试会话: 会话ID={}, 用户ID={}", request.session_id, request.user_id);

        // 1. 查找会话
        let session = self.session_repository
            .find_by_session_id(&request.session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        // 2. 验证用户权限
        if session.user_id != request.user_id {
            return Err(crate::error::Error::service("无权限访问此会话"));
        }

        // 3. 检查会话状态
        if session.session_status == SessionStatus::Completed {
            return Err(crate::error::Error::service("考试已完成，无法恢复"));
        }

        // 4. 构建考试状态
        let exam_state = self.build_exam_state(&session).await?;

        // 5. 构建响应
        let response = ResumeExamSessionResponseDto {
            session_id: session.session_id.clone(),
            exam_state,
        };

        info!("考试会话恢复成功: {}", response.session_id);
        Ok(response)
    }

    /// 取消考试会话
    pub async fn cancel_session(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<()> {
        info!("取消考试会话: 会话ID={}, 用户ID={}", session_id, user_id);

        // 1. 验证会话存在且属于该用户
        let session = self.session_repository
            .find_by_session_id(session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        if session.user_id != user_id {
            return Err(crate::error::Error::service("无权限操作该会话"));
        }

        // 2. 检查会话状态
        if session.session_status != SessionStatus::InProgress {
            return Err(crate::error::Error::service("只能取消进行中的会话"));
        }

        // 3. 更新会话状态为已取消
        let mut updated_session = session;
        updated_session.session_status = SessionStatus::Completed; // 使用Completed状态表示已结束
        updated_session.completed_at = Some(chrono::Utc::now());
        updated_session.updated_at = chrono::Utc::now();

        self.session_repository.update(&updated_session).await?;

        info!("成功取消考试会话: {}", session_id);
        Ok(())
    }

    /// 获取会话状态
    pub async fn get_session_state(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<ExamStateDto> {
        info!("获取会话状态: 会话ID={}, 用户ID={}", session_id, user_id);

        // 1. 验证会话
        if !self.validate_session(session_id, user_id).await? {
            return Err(crate::error::Error::service("会话无效或已过期"));
        }

        // 2. 获取会话信息
        let session = self.session_repository
            .find_by_session_id(session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        // 3. 构建考试状态
        self.build_exam_state(&session).await
    }

    /// 验证会话有效性
    pub async fn validate_session(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<bool> {
        if let Some(session) = self.session_repository.find_by_session_id(session_id).await? {
            Ok(session.user_id == user_id && session.session_status == SessionStatus::InProgress)
        } else {
            Ok(false)
        }
    }

    /// 计算总题数
    fn calculate_total_questions(&self, exam_type: &ExamType) -> i32 {
        match exam_type {
            ExamType::Full => 98,      // 54阅读 + 44数学
            ExamType::Reading => 54,   // 仅阅读
            ExamType::Math => 44,      // 仅数学
        }
    }

    /// 计算总时长（分钟）
    fn calculate_total_time_minutes(&self, exam_type: &ExamType) -> i32 {
        match exam_type {
            ExamType::Full => 134,     // 64阅读 + 70数学
            ExamType::Reading => 64,   // 仅阅读
            ExamType::Math => 70,      // 仅数学
        }
    }

    /// 构建学科信息列表（用于创建会话时）
    async fn build_section_info_list(&self, exam_type: &ExamType) -> Result<Vec<SectionInfoDto>> {
        self.build_section_info_list_with_session(exam_type, None).await
    }

    /// 构建学科信息列表（支持查询已存在的会话）
    async fn build_section_info_list_with_session(
        &self,
        exam_type: &ExamType,
        session_id: Option<&str>
    ) -> Result<Vec<SectionInfoDto>> {
        let mut sections = Vec::new();

        match exam_type {
            ExamType::Full => {
                // 全长考试：阅读 + 数学
                sections.push(self.build_section_info_with_session("阅读", Subject::Reading, session_id).await?);
                sections.push(self.build_section_info_with_session("数学", Subject::Math, session_id).await?);
            }
            ExamType::Reading => {
                // 仅阅读
                sections.push(self.build_section_info_with_session("阅读", Subject::Reading, session_id).await?);
            }
            ExamType::Math => {
                // 仅数学
                sections.push(self.build_section_info_with_session("数学", Subject::Math, session_id).await?);
            }
        }

        Ok(sections)
    }

    /// 构建单个学科信息（支持查询已存在的会话）
    async fn build_section_info_with_session(
        &self,
        section_name: &str,
        subject: Subject,
        session_id: Option<&str>
    ) -> Result<SectionInfoDto> {
        let (question_count, time_limit) = match subject {
            Subject::Reading => (27, 32),
            Subject::Math => (22, 35),
        };

        let mut modules = vec![
            // 模块1：固定配置
            ModuleInfoDto {
                module_type: ModuleType::Module1,
                question_count,
                time_limit_minutes: time_limit,
                difficulty_level: "standard".to_string(),
                status: ModuleStatus::Available,
                adaptive_info: None,
            },
        ];

        // 模块2：根据是否已存在来决定配置
        let module2_info = if let Some(session_id) = session_id {
            // 检查是否已创建模块2
            if let Some(actual_module2_type) = self.adaptive_service.check_module2_exists(session_id, subject).await? {
                // 已创建，返回具体类型
                ModuleInfoDto {
                    module_type: actual_module2_type,
                    question_count,
                    time_limit_minutes: time_limit,
                    difficulty_level: match actual_module2_type {
                        ModuleType::Module2E => "easier".to_string(),
                        ModuleType::Module2H => "harder".to_string(),
                        _ => "standard".to_string(),
                    },
                    status: ModuleStatus::Available,
                    adaptive_info: Some(AdaptiveModuleInfoDto {
                        depends_on: ModuleType::Module1,
                        possible_types: vec![ModuleType::Module2E, ModuleType::Module2H],
                        actual_type: Some(actual_module2_type),
                        performance_score: None, // TODO: 可以从模块1获取成绩
                    }),
                }
            } else {
                // 未创建，返回待定状态
                self.build_pending_module2_info(question_count, time_limit)
            }
        } else {
            // 新会话，返回待定状态
            self.build_pending_module2_info(question_count, time_limit)
        };

        modules.push(module2_info);

        Ok(SectionInfoDto {
            section_name: section_name.to_string(),
            subject,
            modules,
        })
    }

    /// 构建待定状态的模块2信息
    fn build_pending_module2_info(&self, question_count: i32, time_limit: i32) -> ModuleInfoDto {
        ModuleInfoDto {
            module_type: ModuleType::Module2E, // 占位符
            question_count,
            time_limit_minutes: time_limit,
            difficulty_level: "adaptive".to_string(),
            status: ModuleStatus::Pending,
            adaptive_info: Some(AdaptiveModuleInfoDto {
                depends_on: ModuleType::Module1,
                possible_types: vec![ModuleType::Module2E, ModuleType::Module2H],
                actual_type: None,
                performance_score: None,
            }),
        }
    }

    /// 构建考试状态
    async fn build_exam_state(&self, _session: &ExamSession) -> Result<ExamStateDto> {
        // TODO: 实现真实的状态构建逻辑
        // 暂时返回示例数据
        Ok(ExamStateDto {
            current_module: Some(ModuleProgressDto {
                module_type: ModuleType::Module1,
                subject: Subject::Reading,
                subject_name: "阅读".to_string(),
                total_questions: 27,
                answered_questions: 0,
                correct_questions: 0,
                time_limit_seconds: 32 * 60,
                time_used_seconds: 0,
                remaining_time_seconds: Some(32 * 60),
                module_status: ModuleStatus::NotStarted,
                started_at: None,
                completed_at: None,
            }),
            module_progresses: vec![],
            answer_summary: AnswerSummaryDto {
                total_questions: 98, // TODO: 根据考试类型计算
                answered_questions: 0,
                correct_questions: 0,
                skipped_questions: 0,
                accuracy_rate: 0.0,
            },
        })
    }

    /// 转换会话实体为DTO
    #[allow(dead_code)]
    fn convert_to_session_dto(&self, session: ExamSession) -> ExamSessionDto {
        ExamSessionDto {
            session_id: session.session_id,
            user_id: session.user_id,
            paper_id: session.paper_id,
            exam_type: session.exam_type,
            current_subject: session.current_subject,
            current_module_type: session.current_module_type,
            session_status: session.session_status,
            total_time_seconds: session.total_time_seconds,
            reading_time_seconds: session.reading_time_seconds,
            math_time_seconds: session.math_time_seconds,
            created_at: session.created_at,
            updated_at: session.updated_at,
            completed_at: session.completed_at,
        }
    }
}
