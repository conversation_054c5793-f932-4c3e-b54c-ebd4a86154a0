//! 考试成绩服务实现
//!
//! 负责处理考试提交、成绩计算、统计分析等功能

use tracing::info;

use crate::error::Result;
use crate::domain::exam::{
    ExamSession, ExamAnswer, SatPaperQuestion,
    ExamSessionRepository, ExamAnswerRepository, SatPaperRepository,
    SessionStatus, Subject,
};

use super::super::dto::{
    SubmitExamRequestDto, SubmitExamResponseDto,
    ExamScoreDto, SubjectScoreDto, ExamStatisticsDto,
};

/// 成绩服务实现
pub struct ScoreServiceImpl;

impl ScoreServiceImpl {
    pub fn new() -> Self {
        Self
    }

    /// 提交整个考试
    pub async fn submit_exam(
        &self,
        request: SubmitExamRequestDto,
        session_repository: &dyn ExamSessionRepository,
        answer_repository: &dyn ExamAnswerRepository,
        paper_repository: &dyn SatPaperRepository,
    ) -> Result<SubmitExamResponseDto> {
        info!("处理考试提交: 会话ID={}, 用户ID={}", request.session_id, request.user_id);

        // 1. 验证会话有效性
        if !self.validate_session(&request.session_id, request.user_id, session_repository).await? {
            return Err(crate::error::Error::service("会话无效或已过期"));
        }

        // 2. 获取会话信息
        let session = session_repository
            .find_by_session_id(&request.session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        // 3. 检查是否已经提交过
        if session.session_status == SessionStatus::Completed {
            return Err(crate::error::Error::service("考试已经提交过"));
        }

        // 4. 获取所有答题记录
        let all_answers = answer_repository
            .find_by_session_id(&request.session_id)
            .await?;

        // 5. 获取试卷信息
        let _paper = paper_repository
            .find_paper_by_id(session.paper_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("试卷不存在"))?;

        // 6. 获取试卷所有题目
        let all_questions = paper_repository
            .find_questions_by_paper_and_exam_type(session.paper_id, session.exam_type)
            .await?;

        // 7. 计算成绩
        let exam_score = self.calculate_exam_score(&all_answers, &all_questions).await?;
        
        // 8. 计算统计信息
        let statistics = self.calculate_exam_statistics(&all_answers, &all_questions, &session).await?;

        // 9. 更新会话状态为已完成
        let mut updated_session = session.clone();
        updated_session.session_status = SessionStatus::Completed;
        updated_session.completed_at = Some(chrono::Utc::now());
        updated_session.updated_at = chrono::Utc::now();
        
        session_repository.update(&updated_session).await?;

        // 10. 构建响应
        let response = SubmitExamResponseDto {
            success: true,
            session_id: request.session_id.clone(),
            exam_score,
            statistics,
            submitted_at: chrono::Utc::now(),
            total_duration_seconds: (chrono::Utc::now() - session.created_at).num_seconds() as i32,
            exam_status: "completed".to_string(),
        };

        info!("考试提交完成: 会话ID={}, 总分={}/{}", 
              request.session_id, response.exam_score.total_score, response.exam_score.max_score);
        Ok(response)
    }

    /// 验证会话有效性
    async fn validate_session(
        &self,
        session_id: &str,
        user_id: i64,
        session_repository: &dyn ExamSessionRepository,
    ) -> Result<bool> {
        if let Some(session) = session_repository.find_by_session_id(session_id).await? {
            Ok(session.user_id == user_id && session.session_status == SessionStatus::InProgress)
        } else {
            Ok(false)
        }
    }

    /// 计算考试成绩
    async fn calculate_exam_score(
        &self,
        answers: &[ExamAnswer],
        questions: &[SatPaperQuestion],
    ) -> Result<ExamScoreDto> {
        info!("计算考试成绩: 答题数={}, 题目数={}", answers.len(), questions.len());

        // 按学科分组统计
        let mut reading_correct = 0;
        let mut reading_total = 0;
        let mut math_correct = 0;
        let mut math_total = 0;

        // 创建题目ID到学科的映射
        let mut question_subject_map = std::collections::HashMap::new();
        for question in questions {
            question_subject_map.insert(question.question_id, question.subject_id);
        }

        // 统计各学科的答题情况
        for answer in answers {
            if let Some(&subject_id) = question_subject_map.get(&answer.question_id) {
                match subject_id {
                    1 => { // 数学
                        math_total += 1;
                        if answer.is_correct == Some(true) {
                            math_correct += 1;
                        }
                    },
                    14 => { // 语言
                        reading_total += 1;
                        if answer.is_correct == Some(true) {
                            reading_correct += 1;
                        }
                    },
                    _ => {} // 其他学科忽略
                }
            }
        }

        // 计算SAT分数（简化版本）
        // 实际SAT分数计算更复杂，涉及等值化等过程
        let reading_score = self.calculate_sat_score(reading_correct, reading_total, 200, 800);
        let math_score = self.calculate_sat_score(math_correct, math_total, 200, 800);

        let subject_scores = vec![
            SubjectScoreDto {
                subject: Subject::Reading,
                subject_name: "语言".to_string(),
                score: reading_score,
                max_score: 800,
                correct_count: reading_correct,
                total_count: reading_total,
                accuracy_rate: if reading_total > 0 { reading_correct as f64 / reading_total as f64 } else { 0.0 },
            },
            SubjectScoreDto {
                subject: Subject::Math,
                subject_name: "数学".to_string(),
                score: math_score,
                max_score: 800,
                correct_count: math_correct,
                total_count: math_total,
                accuracy_rate: if math_total > 0 { math_correct as f64 / math_total as f64 } else { 0.0 },
            },
        ];

        let total_score = reading_score + math_score;
        let max_score = 1600; // SAT总分1600
        let percentage = (total_score as f64 / max_score as f64) * 100.0;

        Ok(ExamScoreDto {
            total_score,
            max_score,
            percentage,
            subject_scores,
        })
    }

    /// 计算考试统计信息（示例实现）
    /// TODO: 后续替换为基于真实答题数据的统计计算
    async fn calculate_exam_statistics(
        &self,
        answers: &[ExamAnswer],
        questions: &[SatPaperQuestion],
        _session: &ExamSession,
    ) -> Result<ExamStatisticsDto> {
        info!("计算考试统计: 答题数={}, 题目数={}", answers.len(), questions.len());

        // 示例统计数据 - 后续替换为真实计算
        let total_questions = 98; // SAT总题数：54阅读 + 44数学
        let total_answered = 95;  // 示例：完成了95题
        let total_correct = 83;   // 示例：答对83题
        let total_time_seconds = 10800; // 示例：用时3小时
        
        let average_time_per_question = if total_answered > 0 {
            total_time_seconds as f64 / total_answered as f64
        } else {
            0.0
        };

        let completion_rate = total_answered as f64 / total_questions as f64;
        let accuracy_rate = if total_answered > 0 {
            total_correct as f64 / total_answered as f64
        } else {
            0.0
        };

        Ok(ExamStatisticsDto {
            total_answered,
            total_questions,
            total_correct,
            total_time_seconds,
            average_time_per_question,
            completion_rate,
            accuracy_rate,
        })
    }

    /// 计算SAT分数（简化版本）
    ///
    /// # 参数
    /// * `correct` - 答对题数
    /// * `total` - 总题数
    /// * `min_score` - 最低分数
    /// * `max_score` - 最高分数
    fn calculate_sat_score(&self, correct: i32, total: i32, min_score: i32, max_score: i32) -> i32 {
        if total == 0 {
            return min_score;
        }

        let accuracy = correct as f64 / total as f64;

        // 简化的分数映射：
        // 0% -> min_score
        // 100% -> max_score
        // 使用非线性映射，高正确率获得更高分数
        let normalized_score = if accuracy >= 0.95 {
            1.0 // 95%以上正确率获得满分
        } else if accuracy >= 0.85 {
            0.8 + (accuracy - 0.85) * 2.0 // 85%-95%区间快速增长
        } else if accuracy >= 0.70 {
            0.6 + (accuracy - 0.70) * 1.33 // 70%-85%区间中等增长
        } else {
            accuracy * 0.857 // 70%以下线性增长
        };

        let score = min_score + ((max_score - min_score) as f64 * normalized_score) as i32;
        score.max(min_score).min(max_score)
    }
}
