//! 考试数据一致性检查服务
//!
//! 负责验证考试过程中各种数据的一致性，确保数据完整性

use std::sync::Arc;
use tracing::{info, warn};

use crate::error::Result;
use crate::domain::exam::{
    ExamSession, ExamAnswer,
    ExamSessionRepository, ExamAnswerRepository, ExamModuleProgressRepository, SatPaperRepository,
    ModuleType, SessionStatus,
};

/// 数据一致性检查服务
#[derive(Clone)]
pub struct DataConsistencyService {
    session_repository: Arc<dyn ExamSessionRepository>,
    answer_repository: Arc<dyn ExamAnswerRepository>,
    progress_repository: Arc<dyn ExamModuleProgressRepository>,
    paper_repository: Arc<dyn SatPaperRepository>,
}

impl DataConsistencyService {
    pub fn new(
        session_repository: Arc<dyn ExamSessionRepository>,
        answer_repository: Arc<dyn ExamAnswerRepository>,
        progress_repository: Arc<dyn ExamModuleProgressRepository>,
        paper_repository: Arc<dyn SatPaperRepository>,
    ) -> Self {
        Self {
            session_repository,
            answer_repository,
            progress_repository,
            paper_repository,
        }
    }

    /// 全面检查考试数据一致性
    pub async fn check_exam_consistency(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<ConsistencyReport> {
        info!("开始检查考试数据一致性: 会话ID={}, 用户ID={}", session_id, user_id);

        let mut report = ConsistencyReport::new(session_id.to_string(), user_id);

        // 1. 检查会话基础数据
        let session = match self.check_session_consistency(session_id, user_id).await {
            Ok(session) => {
                report.add_check("会话基础数据", true, "会话数据正常".to_string());
                session
            }
            Err(e) => {
                report.add_check("会话基础数据", false, format!("会话数据异常: {}", e));
                return Ok(report);
            }
        };

        // 2. 检查模块进度一致性
        if let Err(e) = self.check_module_progress_consistency(&session, &mut report).await {
            warn!("模块进度一致性检查失败: {}", e);
        }

        // 3. 检查答题记录一致性
        if let Err(e) = self.check_answer_consistency(&session, &mut report).await {
            warn!("答题记录一致性检查失败: {}", e);
        }

        // 4. 检查时间数据一致性（已关闭）
        report.add_check("时间数据一致性", true, "时间管理功能已关闭，跳过时间验证".to_string());

        // 5. 检查分数统计一致性
        if let Err(e) = self.check_score_consistency(&session, &mut report).await {
            warn!("分数统计一致性检查失败: {}", e);
        }

        info!("考试数据一致性检查完成: 会话ID={}, 总检查项={}, 通过项={}", 
              session_id, report.total_checks, report.passed_checks);

        Ok(report)
    }

    /// 检查会话基础数据
    async fn check_session_consistency(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<ExamSession> {
        let session = self.session_repository
            .find_by_session_id(session_id)
            .await?
            .ok_or_else(|| crate::error::Error::not_found("会话不存在"))?;

        if session.user_id != user_id {
            return Err(crate::error::Error::invalid_input("用户ID不匹配"));
        }

        if session.session_status == SessionStatus::Completed {
            return Err(crate::error::Error::invalid_input("会话已完成"));
        }

        Ok(session)
    }

    /// 检查模块进度一致性
    async fn check_module_progress_consistency(
        &self,
        session: &ExamSession,
        report: &mut ConsistencyReport,
    ) -> Result<()> {
        // 获取所有模块进度
        let all_progress = self.progress_repository
            .find_by_session_id(&session.session_id)
            .await?;

        if all_progress.is_empty() {
            report.add_check("模块进度存在性", false, "未找到任何模块进度记录".to_string());
            return Ok(());
        }

        report.add_check("模块进度存在性", true, format!("找到{}个模块进度记录", all_progress.len()));

        // 检查每个模块进度的数据一致性
        for progress in &all_progress {
            let check_name = format!("模块{}数据一致性", progress.module_type);
            
            // 检查基础字段
            if progress.session_id != session.session_id {
                report.add_check(&check_name, false, "会话ID不匹配".to_string());
                continue;
            }

            if progress.user_id != session.user_id {
                report.add_check(&check_name, false, "用户ID不匹配".to_string());
                continue;
            }

            if progress.paper_id != session.paper_id {
                report.add_check(&check_name, false, "试卷ID不匹配".to_string());
                continue;
            }

            // 检查题目数量
            let expected_questions = progress.module_type.question_count(progress.subject);
            if progress.total_questions != expected_questions {
                report.add_check(&check_name, false, 
                    format!("题目数量不正确: 期望={}, 实际={}", expected_questions, progress.total_questions));
                continue;
            }

            // 时间限制检查已关闭
            // 时间管理功能已关闭，跳过时间限制验证

            // 检查答题数量范围
            if progress.answered_questions < 0 || progress.answered_questions > progress.total_questions {
                report.add_check(&check_name, false, 
                    format!("答题数量超出范围: {}/{}", progress.answered_questions, progress.total_questions));
                continue;
            }

            // 检查正确题数范围
            if progress.correct_questions < 0 || progress.correct_questions > progress.answered_questions {
                report.add_check(&check_name, false, 
                    format!("正确题数超出范围: {}/{}", progress.correct_questions, progress.answered_questions));
                continue;
            }

            report.add_check(&check_name, true, "模块进度数据正常".to_string());
        }

        Ok(())
    }

    /// 检查答题记录一致性
    async fn check_answer_consistency(
        &self,
        session: &ExamSession,
        report: &mut ConsistencyReport,
    ) -> Result<()> {
        // 获取所有答题记录
        let all_answers = self.answer_repository
            .find_by_session_id(&session.session_id)
            .await?;

        report.add_check("答题记录存在性", true, format!("找到{}条答题记录", all_answers.len()));

        // 按模块分组检查
        let mut module_answers: std::collections::HashMap<ModuleType, Vec<&ExamAnswer>> = 
            std::collections::HashMap::new();

        for answer in &all_answers {
            module_answers.entry(answer.module_type).or_insert_with(Vec::new).push(answer);
        }

        for (module_type, answers) in module_answers {
            let check_name = format!("模块{}答题记录", module_type);

            // 检查答题记录的基础字段
            for answer in &answers {
                if answer.session_id != session.session_id {
                    report.add_check(&check_name, false, "答题记录会话ID不匹配".to_string());
                    continue;
                }

                if answer.user_id != session.user_id {
                    report.add_check(&check_name, false, "答题记录用户ID不匹配".to_string());
                    continue;
                }

                if answer.paper_id != session.paper_id {
                    report.add_check(&check_name, false, "答题记录试卷ID不匹配".to_string());
                    continue;
                }
            }

            // 检查题目序号的连续性和唯一性
            let mut sequences: Vec<i32> = answers.iter()
                .map(|a| a.module_sequence)
                .collect();
            sequences.sort();

            let expected_sequences: Vec<i32> = (1..=sequences.len() as i32).collect();
            if sequences != expected_sequences {
                report.add_check(&check_name, false, 
                    format!("题目序号不连续: 期望={:?}, 实际={:?}", expected_sequences, sequences));
            } else {
                report.add_check(&check_name, true, "答题记录序号正常".to_string());
            }
        }

        Ok(())
    }

    /// 检查时间数据一致性（已关闭）
    async fn check_time_consistency(
        &self,
        _session: &ExamSession,
        _report: &mut ConsistencyReport,
    ) -> Result<()> {
        // 时间管理功能已关闭，跳过时间数据检查
        info!("时间管理功能已关闭，跳过时间数据一致性检查");
        Ok(())
    }

    /// 检查分数统计一致性
    async fn check_score_consistency(
        &self,
        session: &ExamSession,
        report: &mut ConsistencyReport,
    ) -> Result<()> {
        let all_progress = self.progress_repository
            .find_by_session_id(&session.session_id)
            .await?;

        for progress in &all_progress {
            let check_name = format!("模块{}分数统计", progress.module_type);

            // 获取该模块的答题记录
            let answers = self.answer_repository
                .find_by_session_and_module(&session.session_id, progress.module_type)
                .await?;

            // 检查答题数量一致性
            if answers.len() as i32 != progress.answered_questions {
                report.add_check(&check_name, false, 
                    format!("答题数量不一致: 记录={}, 进度={}", answers.len(), progress.answered_questions));
                continue;
            }

            // 检查正确题数一致性
            let actual_correct = answers.iter()
                .filter(|a| a.is_correct == Some(true))
                .count() as i32;

            if actual_correct != progress.correct_questions {
                report.add_check(&check_name, false, 
                    format!("正确题数不一致: 实际={}, 进度={}", actual_correct, progress.correct_questions));
                continue;
            }

            report.add_check(&check_name, true, "分数统计正常".to_string());
        }

        Ok(())
    }
}

/// 一致性检查报告
#[derive(Debug)]
pub struct ConsistencyReport {
    pub session_id: String,
    pub user_id: i64,
    pub checks: Vec<ConsistencyCheck>,
    pub total_checks: usize,
    pub passed_checks: usize,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

impl ConsistencyReport {
    pub fn new(session_id: String, user_id: i64) -> Self {
        Self {
            session_id,
            user_id,
            checks: Vec::new(),
            total_checks: 0,
            passed_checks: 0,
            created_at: chrono::Utc::now(),
        }
    }

    pub fn add_check(&mut self, name: &str, passed: bool, message: String) {
        self.checks.push(ConsistencyCheck {
            name: name.to_string(),
            passed,
            message,
        });
        self.total_checks += 1;
        if passed {
            self.passed_checks += 1;
        }
    }

    pub fn is_all_passed(&self) -> bool {
        self.passed_checks == self.total_checks
    }

    pub fn success_rate(&self) -> f64 {
        if self.total_checks == 0 {
            1.0
        } else {
            self.passed_checks as f64 / self.total_checks as f64
        }
    }
}

/// 单个一致性检查项
#[derive(Debug)]
pub struct ConsistencyCheck {
    pub name: String,
    pub passed: bool,
    pub message: String,
}
