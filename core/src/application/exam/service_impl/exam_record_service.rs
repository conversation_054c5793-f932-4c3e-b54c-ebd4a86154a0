//! 考试记录应用服务实现
//!
//! 提供考试记录查询、统计和分析功能

use std::sync::Arc;
use async_trait::async_trait;
use tracing::info;

use crate::error::Result;
use super::super::dto::{
    GetExamRecordsRequestDto, ExamRecordsResponseDto,
    GetPaperExamRecordsRequestDto, PaperExamRecordsResponseDto,
    GetExamOverviewRequestDto, ExamOverviewResponseDto,
    GetModuleDetailsRequestDto, ModuleDetailsResponseDto,
    // 新的三层历史记录接口
    GetUserExamHistoryListRequestDto, UserExamHistoryListResponseDto,
    GetExamQuestionsOverviewRequestDto, ExamQuestionsOverviewResponseDto,
    GetModuleDetailedQuestionsRequestDto, ModuleDetailedQuestionsResponseDto,
};

/// 考试记录应用服务接口
#[async_trait]
pub trait ExamRecordApplicationService: Send + Sync {
    /// 获取用户的考试记录列表
    async fn get_exam_records(&self, request: GetExamRecordsRequestDto) -> Result<ExamRecordsResponseDto>;

    /// 获取指定试卷的考试记录（不分页）
    async fn get_paper_exam_records(&self, request: GetPaperExamRecordsRequestDto) -> Result<PaperExamRecordsResponseDto>;

    /// 获取考试题目概览
    async fn get_exam_overview(&self, request: GetExamOverviewRequestDto) -> Result<ExamOverviewResponseDto>;

    /// 获取分模块答题详情
    async fn get_module_details(&self, request: GetModuleDetailsRequestDto) -> Result<ModuleDetailsResponseDto>;

    /// 第一层：获取用户历史答卷记录列表
    async fn get_user_exam_history_list(&self, request: GetUserExamHistoryListRequestDto) -> Result<UserExamHistoryListResponseDto>;

    /// 第二层：获取试卷题目概览（显示所有题目的答题状态）
    async fn get_exam_questions_overview(&self, request: GetExamQuestionsOverviewRequestDto) -> Result<ExamQuestionsOverviewResponseDto>;

    /// 第三层：获取模块详细题目内容和答题情况
    async fn get_module_detailed_questions(&self, request: GetModuleDetailedQuestionsRequestDto) -> Result<ModuleDetailedQuestionsResponseDto>;

    /// 更新考试统计信息（当考试完成时调用）
    async fn update_exam_statistics(&self, user_id: i64, paper_id: i64, exam_type: String) -> Result<()>;

    /// 重新计算用户的所有考试统计信息
    async fn recalculate_user_statistics(&self, user_id: i64) -> Result<()>;
}

/// 考试记录应用服务实现
pub struct ExamRecordServiceImpl {
    exam_record_repository: Arc<dyn crate::domain::exam::repository::ExamRecordRepository>,
    exam_record_domain_service: Arc<crate::domain::exam::services::exam_record_service::ExamRecordDomainService>,
}

impl ExamRecordServiceImpl {
    /// 创建新的考试记录服务实例
    pub fn new(
        exam_record_repository: Arc<dyn crate::domain::exam::repository::ExamRecordRepository>,
    ) -> Self {
        // 创建领域服务
        let exam_record_domain_service = Arc::new(
            crate::domain::exam::services::exam_record_service::ExamRecordDomainService::new(
                exam_record_repository.clone()
            )
        );

        Self {
            exam_record_repository,
            exam_record_domain_service,
        }
    }
}

#[async_trait]
impl ExamRecordApplicationService for ExamRecordServiceImpl {
    /// 获取用户的考试记录列表
    async fn get_exam_records(&self, request: GetExamRecordsRequestDto) -> Result<ExamRecordsResponseDto> {
        info!("获取用户考试记录: user_id={}", request.user_id);

        // TODO: 实现考试记录查询逻辑
        todo!("需要实现考试记录查询逻辑");
    }

    /// 获取指定试卷的考试记录（不分页）
    async fn get_paper_exam_records(&self, request: GetPaperExamRecordsRequestDto) -> Result<PaperExamRecordsResponseDto> {
        info!("获取指定用户试卷考试记录: user_id={}, paper_ids={:?}", request.user_id, request.paper_ids);

        // TODO: 实现试卷考试记录查询逻辑
        todo!("需要实现试卷考试记录查询逻辑");

    }

    /// 获取考试题目概览
    async fn get_exam_overview(&self, request: GetExamOverviewRequestDto) -> Result<ExamOverviewResponseDto> {
        info!("获取考试题目概览: session_id={}, user_id={}", request.session_id, request.user_id);

        // 1. 调用领域服务获取数据
        let overview_data = self.exam_record_domain_service
            .get_exam_overview_data(&request.session_id, request.user_id)
            .await?;

        // 2. 转换为DTO
        Ok(ExamOverviewResponseDto::from_domain(overview_data))
    }

    /// 获取分模块答题详情
    async fn get_module_details(&self, request: GetModuleDetailsRequestDto) -> Result<ModuleDetailsResponseDto> {
        info!("获取分模块答题详情: session_id={}, module_type={}", request.session_id, request.module_type);

        // 1. 解析模块类型
        let module_type = crate::domain::exam::value_objects::ModuleType::from_str(&request.module_type)
            .ok_or_else(|| crate::error::Error::InvalidInput(format!("无效的模块类型: {}", request.module_type)))?;

        // 2. 调用领域服务
        let details_data = self.exam_record_domain_service
            .get_module_details_data(&request.session_id, request.user_id, module_type)
            .await?;

        // 3. 转换为DTO
        Ok(ModuleDetailsResponseDto::from_domain(details_data))
    }

    /// 第一层：获取用户历史答卷记录列表
    async fn get_user_exam_history_list(&self, request: GetUserExamHistoryListRequestDto) -> Result<UserExamHistoryListResponseDto> {
        info!("获取用户历史答卷记录列表: user_id={}", request.user_id);

        // TODO: 实现获取用户历史记录列表的逻辑
        // 1. 获取用户的所有考试记录
        // 2. 分页处理
        // 3. 组装响应数据
        todo!("需要实现获取用户历史记录列表的逻辑");
    }

    /// 第二层：获取试卷题目概览（显示所有题目的答题状态）
    async fn get_exam_questions_overview(&self, request: GetExamQuestionsOverviewRequestDto) -> Result<ExamQuestionsOverviewResponseDto> {
        info!("获取试卷题目概览: session_id={}, user_id={}", request.session_id, request.user_id);

        // TODO: 实现获取试卷题目概览的逻辑
        // 1. 根据session_id获取考试基本信息
        // 2. 获取所有模块的题目状态（不包含题目内容）
        // 3. 组装响应数据
        todo!("需要实现获取试卷题目概览的逻辑");
    }

    /// 第三层：获取模块详细题目内容和答题情况
    async fn get_module_detailed_questions(&self, request: GetModuleDetailedQuestionsRequestDto) -> Result<ModuleDetailedQuestionsResponseDto> {
        info!("获取模块详细题目: session_id={}, module_type={}", request.session_id, request.module_type);

        // 1. 解析模块类型
        let module_type = crate::domain::exam::value_objects::ModuleType::from_str(&request.module_type)
            .ok_or_else(|| crate::error::Error::InvalidInput(format!("无效的模块类型: {}", request.module_type)))?;

        // TODO: 实现获取模块详细题目的逻辑
        // 1. 根据session_id和module_type获取模块信息
        // 2. 获取该模块的所有题目内容和答题情况
        // 3. 组装响应数据
        todo!("需要实现获取模块详细题目的逻辑");
    }

    /// 更新考试统计信息（应用层计算）
    async fn update_exam_statistics(&self, user_id: i64, paper_id: i64, exam_type: String) -> Result<()> {
        info!("更新考试统计信息: user_id={}, paper_id={}, exam_type={}", user_id, paper_id, exam_type);

        // TODO: 实现考试统计信息更新逻辑
        todo!("需要实现考试统计信息更新逻辑");

    }

    /// 重新计算用户的所有考试统计信息
    async fn recalculate_user_statistics(&self, user_id: i64) -> Result<()> {
        info!("重新计算用户统计信息: user_id={}", user_id);

        // TODO: 实现用户统计信息重新计算逻辑
        todo!("需要实现用户统计信息重新计算逻辑");
    }
}

impl ExamRecordServiceImpl {
    // TODO: 添加新的转换方法，用于处理三层历史记录接口的数据转换
}


