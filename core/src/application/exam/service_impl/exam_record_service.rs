//! 考试记录应用服务实现
//!
//! 提供考试记录查询、统计和分析功能

use std::sync::Arc;
use async_trait::async_trait;
use tracing::info;

use crate::error::Result;
use super::super::dto::{
    GetExamRecordsRequestDto, ExamRecordsResponseDto,
    GetPaperExamRecordsRequestDto, PaperExamRecordsResponseDto,
    GetExamOverviewRequestDto, ExamOverviewResponseDto,
    GetModuleDetailsRequestDto, ModuleDetailsResponseDto,
    GetExamHistoryQuestionsRequestDto, ExamHistoryQuestionsResponseDto,
    GetModuleDetailedQuestionsRequestDto, ModuleDetailedQuestionsResponseDto,
};

/// 考试记录应用服务接口
#[async_trait]
pub trait ExamRecordApplicationService: Send + Sync {
    /// 获取用户的考试记录列表
    async fn get_exam_records(&self, request: GetExamRecordsRequestDto) -> Result<ExamRecordsResponseDto>;

    /// 获取指定试卷的考试记录（不分页）
    async fn get_paper_exam_records(&self, request: GetPaperExamRecordsRequestDto) -> Result<PaperExamRecordsResponseDto>;

    /// 获取考试题目概览
    async fn get_exam_overview(&self, request: GetExamOverviewRequestDto) -> Result<ExamOverviewResponseDto>;

    /// 获取分模块答题详情
    async fn get_module_details(&self, request: GetModuleDetailsRequestDto) -> Result<ModuleDetailsResponseDto>;

    /// 获取历史记录的所有题目列表和答题情况
    async fn get_exam_history_questions(&self, request: GetExamHistoryQuestionsRequestDto) -> Result<ExamHistoryQuestionsResponseDto>;

    /// 获取单个模块的详细题目和答题情况
    async fn get_module_detailed_questions(&self, request: GetModuleDetailedQuestionsRequestDto) -> Result<ModuleDetailedQuestionsResponseDto>;

    /// 更新考试统计信息（当考试完成时调用）
    async fn update_exam_statistics(&self, user_id: i64, paper_id: i64, exam_type: String) -> Result<()>;

    /// 重新计算用户的所有考试统计信息
    async fn recalculate_user_statistics(&self, user_id: i64) -> Result<()>;
}

/// 考试记录应用服务实现
pub struct ExamRecordServiceImpl {
    exam_record_repository: Arc<dyn crate::domain::exam::repository::ExamRecordRepository>,
    exam_record_domain_service: Arc<crate::domain::exam::services::exam_record_service::ExamRecordDomainService>,
}

impl ExamRecordServiceImpl {
    /// 创建新的考试记录服务实例
    pub fn new(
        exam_record_repository: Arc<dyn crate::domain::exam::repository::ExamRecordRepository>,
    ) -> Self {
        // 创建领域服务
        let exam_record_domain_service = Arc::new(
            crate::domain::exam::services::exam_record_service::ExamRecordDomainService::new(
                exam_record_repository.clone()
            )
        );

        Self {
            exam_record_repository,
            exam_record_domain_service,
        }
    }
}

#[async_trait]
impl ExamRecordApplicationService for ExamRecordServiceImpl {
    /// 获取用户的考试记录列表
    async fn get_exam_records(&self, request: GetExamRecordsRequestDto) -> Result<ExamRecordsResponseDto> {
        info!("获取用户考试记录: user_id={}", request.user_id);

        // TODO: 实现考试记录查询逻辑
        todo!("需要实现考试记录查询逻辑");
    }

    /// 获取指定试卷的考试记录（不分页）
    async fn get_paper_exam_records(&self, request: GetPaperExamRecordsRequestDto) -> Result<PaperExamRecordsResponseDto> {
        info!("获取指定用户试卷考试记录: user_id={}, paper_ids={:?}", request.user_id, request.paper_ids);

        // TODO: 实现试卷考试记录查询逻辑
        todo!("需要实现试卷考试记录查询逻辑");

    }

    /// 获取考试题目概览
    async fn get_exam_overview(&self, request: GetExamOverviewRequestDto) -> Result<ExamOverviewResponseDto> {
        info!("获取考试题目概览: session_id={}, user_id={}", request.session_id, request.user_id);

        // 1. 调用领域服务获取数据
        let overview_data = self.exam_record_domain_service
            .get_exam_overview_data(&request.session_id, request.user_id)
            .await?;

        // 2. 转换为DTO
        Ok(ExamOverviewResponseDto::from_domain(overview_data))
    }

    /// 获取分模块答题详情
    async fn get_module_details(&self, request: GetModuleDetailsRequestDto) -> Result<ModuleDetailsResponseDto> {
        info!("获取分模块答题详情: session_id={}, module_type={}", request.session_id, request.module_type);

        // 1. 解析模块类型
        let module_type = crate::domain::exam::value_objects::ModuleType::from_str(&request.module_type)
            .ok_or_else(|| crate::error::Error::InvalidInput(format!("无效的模块类型: {}", request.module_type)))?;

        // 2. 调用领域服务
        let details_data = self.exam_record_domain_service
            .get_module_details_data(&request.session_id, request.user_id, module_type)
            .await?;

        // 3. 转换为DTO
        Ok(ModuleDetailsResponseDto::from_domain(details_data))
    }

    /// 获取历史记录的所有题目列表和答题情况
    async fn get_exam_history_questions(&self, request: GetExamHistoryQuestionsRequestDto) -> Result<ExamHistoryQuestionsResponseDto> {
        info!("获取历史记录所有题目: session_id={}, user_id={}", request.session_id, request.user_id);

        // 1. 调用领域服务获取数据
        let history_data = self.exam_record_domain_service
            .get_exam_history_questions(&request.session_id, request.user_id)
            .await?;

        // 2. 转换为DTO
        Ok(Self::convert_exam_history_to_dto(history_data))
    }

    /// 获取单个模块的详细题目和答题情况
    async fn get_module_detailed_questions(&self, request: GetModuleDetailedQuestionsRequestDto) -> Result<ModuleDetailedQuestionsResponseDto> {
        info!("获取模块详细题目: session_id={}, module_type={}", request.session_id, request.module_type);

        // 1. 解析模块类型
        let module_type = crate::domain::exam::value_objects::ModuleType::from_str(&request.module_type)
            .ok_or_else(|| crate::error::Error::InvalidInput(format!("无效的模块类型: {}", request.module_type)))?;

        // 2. 调用领域服务获取数据
        let module_data = self.exam_record_domain_service
            .get_module_detailed_questions(&request.session_id, request.user_id, module_type)
            .await?;

        // 3. 转换为DTO
        Ok(Self::convert_module_detailed_to_dto(module_data))
    }

    /// 更新考试统计信息（应用层计算）
    async fn update_exam_statistics(&self, user_id: i64, paper_id: i64, exam_type: String) -> Result<()> {
        info!("更新考试统计信息: user_id={}, paper_id={}, exam_type={}", user_id, paper_id, exam_type);

        // TODO: 实现考试统计信息更新逻辑
        todo!("需要实现考试统计信息更新逻辑");

    }

    /// 重新计算用户的所有考试统计信息
    async fn recalculate_user_statistics(&self, user_id: i64) -> Result<()> {
        info!("重新计算用户统计信息: user_id={}", user_id);

        // TODO: 实现用户统计信息重新计算逻辑
        todo!("需要实现用户统计信息重新计算逻辑");
    }
}

impl ExamRecordServiceImpl {
    /// 转换考试历史数据为DTO
    fn convert_exam_history_to_dto(
        history_data: crate::domain::exam::services::exam_record_service::ExamHistoryData
    ) -> ExamHistoryQuestionsResponseDto {
        use super::super::dto::{ExamHistoryQuestionsResponseDto, ExamHistoryModuleDto, ExamHistoryQuestionDto};

        ExamHistoryQuestionsResponseDto {
            session_id: history_data.session_id,
            paper_id: history_data.paper_id,
            paper_name: history_data.paper_name,
            exam_type: history_data.exam_type.to_string(),
            exam_status: history_data.exam_status,
            total_score: history_data.total_score,
            reading_score: history_data.reading_score,
            math_score: history_data.math_score,
            accuracy_rate: history_data.accuracy_rate,
            completed_at: history_data.completed_at,
            duration_minutes: history_data.duration_minutes,
            modules: history_data.modules.into_iter().map(|module| {
                ExamHistoryModuleDto {
                    module_type: module.module_type.to_string(),
                    subject: module.subject.to_string(),
                    module_score: module.module_score,
                    module_accuracy: module.module_accuracy,
                    module_duration: module.module_duration,
                    questions: module.questions.into_iter().map(|question| {
                        ExamHistoryQuestionDto {
                            question_id: question.question_id,
                            module_sequence: question.module_sequence,
                            question_content: question.question_content,
                            options: question.options,
                            correct_answer: question.correct_answer,
                            user_answer: question.user_answer,
                            answer_status: question.answer_status,
                            response_time_seconds: question.response_time_seconds,
                            difficulty: question.difficulty,
                            knowledge_point_id: question.knowledge_point_id,
                            knowledge_point_name: question.knowledge_point_name,
                        }
                    }).collect(),
                }
            }).collect(),
        }
    }

    /// 转换模块详细数据为DTO
    fn convert_module_detailed_to_dto(
        module_data: crate::domain::exam::services::exam_record_service::ModuleDetailedData
    ) -> ModuleDetailedQuestionsResponseDto {
        use super::super::dto::{ModuleDetailedQuestionsResponseDto, ExamHistoryQuestionDto};

        ModuleDetailedQuestionsResponseDto {
            session_id: module_data.session_id,
            module_type: module_data.module_type.to_string(),
            subject: module_data.subject.to_string(),
            module_score: module_data.module_score,
            module_accuracy: module_data.module_accuracy,
            module_duration: module_data.module_duration,
            module_status: module_data.module_status,
            questions: module_data.questions.into_iter().map(|question| {
                ExamHistoryQuestionDto {
                    question_id: question.question_id,
                    module_sequence: question.module_sequence,
                    question_content: question.question_content,
                    options: question.options,
                    correct_answer: question.correct_answer,
                    user_answer: question.user_answer,
                    answer_status: question.answer_status,
                    response_time_seconds: question.response_time_seconds,
                    difficulty: question.difficulty,
                    knowledge_point_id: question.knowledge_point_id,
                    knowledge_point_name: question.knowledge_point_name,
                }
            }).collect(),
        }
    }
}


