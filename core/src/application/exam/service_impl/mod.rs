//! 考试应用服务实现模块
//!
//! 将考试应用服务按功能拆分为多个子模块

use std::sync::Arc;
use async_trait::async_trait;
use tracing::info;

use crate::error::Result;
use crate::domain::exam::{
    ExamSessionDomainService, ExamQuestionDomainService, AdaptiveModuleService,
    ExamSessionRepository, ExamModuleProgressRepository, ExamAnswerRepository,
    SatPaperRepository,
};
use crate::application::question::service::QuestionApplicationService;

use super::service::ExamApplicationService;
use super::dto::{
    CreateExamSessionRequestDto, CreateExamSessionResponseDto,
    ResumeExamSessionRequestDto, ResumeExamSessionResponseDto,
    StartModuleRequestDto, StartModuleResponseDto,
    SubmitModuleRequestDto, SubmitModuleResponseDto,
    SubmitAnswerRequestDto, SubmitAnswerResponseDto,
    SubmitExamRequestDto, SubmitExamResponseDto,
    ExamStateDto,
    // 增强接口DTO
    ModuleTransitionRequestDto, ModuleTransitionResponseDto,
    EnhancedSubmitAnswerRequestDto, EnhancedSubmitAnswerResponseDto,
};

// 子模块
mod session_service;
mod module_service;
mod module_submit_service;
mod data_consistency_service;
mod answer_service;
mod score_service;
mod helper;
mod enhanced_exam_service;
mod exam_record_service;
mod exam_record_service_factory;

// 重新导出子模块的公共接口
pub use session_service::SessionServiceImpl;
pub use module_service::ModuleServiceImpl;
pub use module_submit_service::ModuleSubmitServiceImpl;
pub use data_consistency_service::{DataConsistencyService, ConsistencyReport};
pub use answer_service::AnswerServiceImpl;
pub use score_service::ScoreServiceImpl;
pub use helper::HelperServiceImpl;
pub use enhanced_exam_service::{EnhancedExamServiceImpl, ExamRepositories as EnhancedExamRepositories};
pub use exam_record_service::{ExamRecordServiceImpl, ExamRecordApplicationService};

/// 仓储依赖组合
///
/// 将所有仓储依赖组合到一个结构体中，避免重复传递
#[derive(Clone)]
pub struct ExamRepositories {
    pub session: Arc<dyn ExamSessionRepository>,
    pub progress: Arc<dyn ExamModuleProgressRepository>,
    pub answer: Arc<dyn ExamAnswerRepository>,
    pub paper: Arc<dyn SatPaperRepository>,
}

/// 领域服务依赖组合
///
/// 将所有领域服务组合到一个结构体中
#[derive(Clone)]
pub struct ExamDomainServices {
    pub session: Arc<ExamSessionDomainService>,
    pub question: Arc<ExamQuestionDomainService>,
}

/// 外部服务依赖组合
///
/// 将所有外部服务组合到一个结构体中
#[derive(Clone)]
pub struct ExamExternalServices {
    pub question: Arc<dyn QuestionApplicationService>,
}

/// 考试应用服务实现
///
/// 采用组合式架构，通过依赖组合减少重复，提高可维护性
pub struct ExamApplicationServiceImpl {
    /// 仓储依赖组合
    repositories: ExamRepositories,

    /// 子服务实现 - 使用函数式组合而非对象持有
    session_service: SessionServiceImpl,
    module_service: ModuleServiceImpl,
    module_submit_service: ModuleSubmitServiceImpl,
    answer_service: AnswerServiceImpl,
    score_service: ScoreServiceImpl,
    #[allow(dead_code)]
    helper_service: HelperServiceImpl,
}

impl ExamApplicationServiceImpl {
    /// 创建新的考试应用服务实例
    ///
    /// 使用组合式依赖注入，减少参数传递和重复持有
    pub fn new(
        session_domain_service: Arc<ExamSessionDomainService>,
        question_domain_service: Arc<ExamQuestionDomainService>,
        session_repository: Arc<dyn ExamSessionRepository>,
        progress_repository: Arc<dyn ExamModuleProgressRepository>,
        answer_repository: Arc<dyn ExamAnswerRepository>,
        paper_repository: Arc<dyn SatPaperRepository>,
        question_service: Arc<dyn QuestionApplicationService>,
    ) -> Self {
        // 组合依赖
        let repositories = ExamRepositories {
            session: session_repository,
            progress: progress_repository,
            answer: answer_repository,
            paper: paper_repository,
        };

        let domain_services = ExamDomainServices {
            session: session_domain_service,
            question: question_domain_service,
        };

        let _external_services = ExamExternalServices {
            question: question_service,
        };

        // 创建自适应模块服务
        let adaptive_service = Arc::new(AdaptiveModuleService::new(
            repositories.answer.clone(),
            repositories.progress.clone(),
        ));

        // 创建子服务实例 - 传递组合后的依赖
        let session_service = SessionServiceImpl::new(
            domain_services.session.clone(),
            repositories.session.clone(),
            repositories.paper.clone(),
            adaptive_service.clone(),
        );

        let module_service = ModuleServiceImpl::new(
            domain_services.question.clone(),
            repositories.progress.clone(),
            repositories.paper.clone(),
            repositories.session.clone(),
        );

        let module_submit_service = ModuleSubmitServiceImpl::new(
            repositories.session.clone(),
            repositories.answer.clone(),
            repositories.progress.clone(),
            repositories.paper.clone(),
            adaptive_service.clone(),
            domain_services.question.clone(),
        );

        let answer_service = AnswerServiceImpl::new(
            repositories.answer.clone(),
            repositories.session.clone(),
            repositories.progress.clone(),
            repositories.paper.clone(),
        );

        let score_service = ScoreServiceImpl::new();
        let helper_service = HelperServiceImpl::new();

        Self {
            repositories,
            session_service,
            module_service,
            module_submit_service,
            answer_service,
            score_service,
            helper_service,
        }
    }
}

#[async_trait]
impl ExamApplicationService for ExamApplicationServiceImpl {
    async fn create_session(
        &self,
        request: CreateExamSessionRequestDto,
    ) -> Result<CreateExamSessionResponseDto> {
        info!("委托会话服务处理创建会话请求");
        self.session_service.create_session(request).await
    }

    async fn resume_session(
        &self,
        request: ResumeExamSessionRequestDto,
    ) -> Result<ResumeExamSessionResponseDto> {
        info!("委托会话服务处理恢复会话请求");
        self.session_service.resume_session(request).await
    }

    async fn cancel_session(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<()> {
        info!("委托会话服务处理取消会话请求");
        self.session_service.cancel_session(session_id, user_id).await
    }

    async fn get_session_state(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<ExamStateDto> {
        info!("委托会话服务处理获取会话状态请求");
        self.session_service.get_session_state(session_id, user_id).await
    }

    async fn start_module(
        &self,
        request: StartModuleRequestDto,
    ) -> Result<StartModuleResponseDto> {
        info!("委托模块服务处理开始模块请求");
        self.module_service.start_module(request).await
    }

    async fn validate_session(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<bool> {
        info!("委托会话服务处理验证会话请求");
        self.session_service.validate_session(session_id, user_id).await
    }

    async fn submit_answer(
        &self,
        request: SubmitAnswerRequestDto,
    ) -> Result<SubmitAnswerResponseDto> {
        info!("委托答题服务处理提交答案请求");
        self.answer_service.submit_answer(request).await
    }

    async fn submit_module(
        &self,
        request: SubmitModuleRequestDto,
    ) -> Result<SubmitModuleResponseDto> {
        info!("委托模块提交服务处理提交模块请求");
        self.module_submit_service.submit_module(request).await
    }

    async fn submit_exam(
        &self,
        request: SubmitExamRequestDto,
    ) -> Result<SubmitExamResponseDto> {
        info!("委托成绩服务处理提交考试请求");
        self.score_service.submit_exam(
            request,
            &*self.repositories.session,
            &*self.repositories.answer,
            &*self.repositories.paper,
        ).await
    }



    // ==================== 增强接口实现 ====================



    async fn transition_module(
        &self,
        request: ModuleTransitionRequestDto,
    ) -> Result<ModuleTransitionResponseDto> {
        info!("处理模块切换请求: 会话ID={}", request.session_id);

        // 创建增强服务实例并委托处理
        let enhanced_service = EnhancedExamServiceImpl::new(
            self.session_service.clone(),
            self.module_service.clone(),
            self.module_submit_service.clone(),
            self.answer_service.clone(),
            EnhancedExamRepositories {
                session: self.repositories.session.clone(),
                answer: self.repositories.answer.clone(),
                progress: self.repositories.progress.clone(),
                paper: self.repositories.paper.clone(),
            },
        );

        enhanced_service.transition_module(request).await
    }

    async fn submit_answer_enhanced(
        &self,
        request: EnhancedSubmitAnswerRequestDto,
    ) -> Result<EnhancedSubmitAnswerResponseDto> {
        info!("处理增强答题提交请求: 会话ID={}, 题目ID={}", request.session_id, request.question_id);

        // 创建增强服务实例并委托处理
        let enhanced_service = EnhancedExamServiceImpl::new(
            self.session_service.clone(),
            self.module_service.clone(),
            self.module_submit_service.clone(),
            self.answer_service.clone(),
            EnhancedExamRepositories {
                session: self.repositories.session.clone(),
                answer: self.repositories.answer.clone(),
                progress: self.repositories.progress.clone(),
                paper: self.repositories.paper.clone(),
            },
        );

        enhanced_service.submit_answer_enhanced(request).await
    }


}
