//! 增强版考试应用服务实现
//!
//! 提供优化后的考试接口，减少前端调用复杂度

use std::sync::Arc;
use async_trait::async_trait;
use tracing::{info, warn};
use chrono::Utc;

use crate::error::Result;
use crate::domain::exam::{
    ExamSessionRepository, ExamAnswerRepository, ExamModuleProgressRepository,
    SatPaperRepository, ModuleStatus, ModuleType,
};

use super::super::{
    ExamApplicationService,
    dto::{
        ModuleTransitionRequestDto, ModuleTransitionResponseDto, ModuleTransitionAction,
        EnhancedSubmitAnswerRequestDto, EnhancedSubmitAnswerResponseDto,
        SessionInfoDto,
        ModuleCompletionStatusDto, SuggestedActionDto, SuggestedActionType,
        ModuleDetailDto, AnswerHistoryDto, TimeInfoDto, RecommendationDto, RecommendationType,
        // 复用现有DTO
        CreateExamSessionRequestDto, StartModuleRequestDto, SubmitModuleRequestDto,
    },
};

use super::{
    SessionServiceImpl, ModuleServiceImpl, ModuleSubmitServiceImpl,
    AnswerServiceImpl,
};

/// 增强版考试应用服务实现
pub struct EnhancedExamServiceImpl {
    // 复用现有服务
    session_service: SessionServiceImpl,
    module_service: ModuleServiceImpl,
    module_submit_service: ModuleSubmitServiceImpl,
    answer_service: AnswerServiceImpl,

    // 仓储依赖
    repositories: ExamRepositories,
}

/// 考试仓储集合
pub struct ExamRepositories {
    pub session: Arc<dyn ExamSessionRepository>,
    pub answer: Arc<dyn ExamAnswerRepository>,
    pub progress: Arc<dyn ExamModuleProgressRepository>,
    pub paper: Arc<dyn SatPaperRepository>,
}

impl EnhancedExamServiceImpl {
    /// 创建新的增强版考试服务实例
    pub fn new(
        session_service: SessionServiceImpl,
        module_service: ModuleServiceImpl,
        module_submit_service: ModuleSubmitServiceImpl,
        answer_service: AnswerServiceImpl,
        repositories: ExamRepositories,
    ) -> Self {
        Self {
            session_service,
            module_service,
            module_submit_service,
            answer_service,
            repositories,
        }
    }

    /// 检查用户是否有未完成的会话
    async fn find_incomplete_session(&self, user_id: i64) -> Result<Option<String>> {
        info!("检查用户未完成会话: 用户ID={}", user_id);

        let session = self.repositories.session
            .find_active_by_user_id(user_id)
            .await?;

        if let Some(session) = session {
            info!("找到未完成会话: {}", session.session_id);
            Ok(Some(session.session_id))
        } else {
            info!("未找到未完成会话");
            Ok(None)
        }
    }

    /// 构建会话信息DTO
    async fn build_session_info(&self, session_id: &str) -> Result<SessionInfoDto> {
        let session = self.repositories.session
            .find_by_session_id(session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        let paper = self.repositories.paper
            .find_paper_by_id(session.paper_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("试卷不存在"))?;

        Ok(SessionInfoDto {
            session_id: session.session_id,
            paper_id: session.paper_id,
            paper_name: paper.paper_name,
            exam_type: session.exam_type,
            session_status: session.session_status,
            created_at: session.created_at,
            total_questions: 98, // SAT固定题目数量：54阅读+44数学
            total_time_minutes: 134, // SAT固定时间：64分钟阅读+70分钟数学
        })
    }





    /// 分析模块完成状态
    async fn analyze_module_completion(
        &self,
        session_id: &str,
        module_type: crate::domain::exam::ModuleType,
    ) -> Result<ModuleCompletionStatusDto> {
        let answers = self.repositories.answer
            .find_by_session_and_module(session_id, module_type)
            .await?;

        let progress = self.repositories.progress
            .find_by_session_and_module(session_id, module_type)
            .await?;

        let total_questions = progress.map(|p| p.total_questions).unwrap_or(22); // SAT默认每模块22题
        let answered_count = answers.len() as i32;
        let completion_percentage = (answered_count as f64 / total_questions as f64) * 100.0;
        
        Ok(ModuleCompletionStatusDto {
            is_module_completed: answered_count >= total_questions,
            completion_percentage,
            remaining_questions: (total_questions - answered_count).max(0),
            can_submit_module: answered_count > 0, // 至少答了一题就可以提交
        })
    }

    /// 生成建议操作
    fn generate_suggested_action(
        &self,
        completion_status: &ModuleCompletionStatusDto,
        remaining_time_seconds: i32,
    ) -> SuggestedActionDto {
        if completion_status.is_module_completed {
            SuggestedActionDto {
                action_type: SuggestedActionType::SubmitModule,
                description: "模块已完成，建议提交并进入下一模块".to_string(),
                is_urgent: false,
                parameters: None,
            }
        } else if remaining_time_seconds < 300 { // 少于5分钟
            SuggestedActionDto {
                action_type: SuggestedActionType::SubmitModule,
                description: "时间不足，建议尽快提交当前模块".to_string(),
                is_urgent: true,
                parameters: Some(serde_json::json!({"remaining_seconds": remaining_time_seconds})),
            }
        } else if completion_status.remaining_questions <= 5 {
            SuggestedActionDto {
                action_type: SuggestedActionType::ContinueAnswering,
                description: format!("还剩{}题，继续答题", completion_status.remaining_questions),
                is_urgent: false,
                parameters: None,
            }
        } else {
            SuggestedActionDto {
                action_type: SuggestedActionType::ContinueAnswering,
                description: "继续答题，合理分配时间".to_string(),
                is_urgent: false,
                parameters: None,
            }
        }
    }
}

#[async_trait]
impl ExamApplicationService for EnhancedExamServiceImpl {


    /// 模块切换（合并接口）
    async fn transition_module(
        &self,
        request: ModuleTransitionRequestDto,
    ) -> Result<ModuleTransitionResponseDto> {
        info!("开始模块切换: 会话ID={}, 操作={:?}", request.session_id, request.action);

        let mut executed_actions = Vec::new();
        let mut submit_result = None;
        let mut start_result = None;

        // 1. 执行提交操作（如果需要）
        if matches!(request.action, ModuleTransitionAction::SubmitOnly | ModuleTransitionAction::SubmitAndNext) {
            if let Some(current_module) = request.current_module_type {
                info!("提交当前模块: {:?}", current_module);
                let submit_request = SubmitModuleRequestDto {
                    session_id: request.session_id.clone(),
                    user_id: request.user_id,
                    module_type: current_module,
                    force_submit: request.force_submit,
                };

                submit_result = Some(self.module_submit_service.submit_module(submit_request).await?);
                executed_actions.push(format!("提交模块: {:?}", current_module));
            }
        }

        // 2. 执行开始操作（如果需要）
        if matches!(request.action, ModuleTransitionAction::StartNext | ModuleTransitionAction::SubmitAndNext) {
            // 智能确定下一个模块类型
            let next_module = self.determine_next_module_type(&request.session_id, request.user_id).await?;
            info!("智能确定下一模块: {:?}", next_module);

            let start_request = StartModuleRequestDto {
                session_id: request.session_id.clone(),
                module_type: next_module,
                user_id: request.user_id,
            };

            start_result = Some(self.module_service.start_module(start_request).await?);
            executed_actions.push(format!("智能开始模块: {:?}", next_module));
        }

        // 3. 获取完整考试状态
        let exam_state = self.session_service.get_session_state(&request.session_id, request.user_id).await?;

        // 4. 确定下一步操作
        let next_action = if let Some(submit_res) = &submit_result {
            submit_res.next_action.clone()
        } else {
            // 默认下一步操作
            crate::application::exam::dto::NextActionDto {
                action_type: crate::application::exam::dto::NextActionType::StartModule2,
                description: "继续下一步".to_string(),
                can_continue: true,
                suggested_wait_seconds: None,
            }
        };

        let response = ModuleTransitionResponseDto {
            success: true,
            executed_actions,
            submit_result,
            start_result,
            exam_state,
            next_action,
            message: "模块切换完成".to_string(),
        };

        info!("模块切换完成: 会话ID={}", request.session_id);
        Ok(response)
    }

    /// 增强答题提交
    async fn submit_answer_enhanced(
        &self,
        request: EnhancedSubmitAnswerRequestDto,
    ) -> Result<EnhancedSubmitAnswerResponseDto> {
        info!("增强答题提交: 会话ID={}, 题目ID={}", request.session_id, request.question_id);

        // 1. 提交答案
        let submit_request = crate::application::exam::dto::SubmitAnswerRequestDto {
            session_id: request.session_id.clone(),
            user_id: request.user_id,
            question_id: request.question_id,
            student_answer: request.student_answer,
            time_spent_seconds: request.time_spent_seconds,
            module_type: request.module_type,
            question_sequence: request.question_sequence,
        };

        let answer_result = self.answer_service.submit_answer(submit_request).await?;

        // 2. 获取完整会话状态（可选）
        let session_state = if request.include_session_state.unwrap_or(false) {
            Some(self.session_service.get_session_state(&request.session_id, request.user_id).await?)
        } else {
            None
        };

        // 3. 获取下一题信息（可选）
        let next_question = if request.include_next_question.unwrap_or(false) {
            // TODO: 实现获取下一题逻辑
            None
        } else {
            None
        };

        // 4. 分析模块完成状态
        let module_completion = self.analyze_module_completion(
            &request.session_id,
            request.module_type,
        ).await?;

        // 5. 生成建议操作
        let remaining_time = answer_result.module_progress.remaining_time_seconds.unwrap_or(0);
        let suggested_action = self.generate_suggested_action(&module_completion, remaining_time);

        let response = EnhancedSubmitAnswerResponseDto {
            answer_result,
            session_state,
            next_question,
            module_completion,
            suggested_action,
        };

        info!("增强答题提交完成: 会话ID={}", request.session_id);
        Ok(response)
    }



    // 委托给原有服务的方法实现
    async fn create_session(
        &self,
        request: CreateExamSessionRequestDto,
    ) -> Result<crate::application::exam::dto::CreateExamSessionResponseDto> {
        self.session_service.create_session(request).await
    }

    async fn resume_session(
        &self,
        request: crate::application::exam::dto::ResumeExamSessionRequestDto,
    ) -> Result<crate::application::exam::dto::ResumeExamSessionResponseDto> {
        self.session_service.resume_session(request).await
    }

    async fn cancel_session(&self, session_id: &str, user_id: i64) -> Result<()> {
        self.session_service.cancel_session(session_id, user_id).await
    }

    async fn start_module(
        &self,
        request: StartModuleRequestDto,
    ) -> Result<crate::application::exam::dto::StartModuleResponseDto> {
        self.module_service.start_module(request).await
    }

    async fn get_session_state(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<crate::application::exam::dto::ExamStateDto> {
        self.session_service.get_session_state(session_id, user_id).await
    }

    async fn validate_session(&self, session_id: &str, user_id: i64) -> Result<bool> {
        self.session_service.validate_session(session_id, user_id).await
    }

    async fn submit_answer(
        &self,
        request: crate::application::exam::dto::SubmitAnswerRequestDto,
    ) -> Result<crate::application::exam::dto::SubmitAnswerResponseDto> {
        self.answer_service.submit_answer(request).await
    }

    async fn submit_module(
        &self,
        request: SubmitModuleRequestDto,
    ) -> Result<crate::application::exam::dto::SubmitModuleResponseDto> {
        self.module_submit_service.submit_module(request).await
    }

    async fn submit_exam(
        &self,
        request: crate::application::exam::dto::SubmitExamRequestDto,
    ) -> Result<crate::application::exam::dto::SubmitExamResponseDto> {
        // 委托给分数服务
        todo!("实现考试提交逻辑")
    }


}

impl EnhancedExamServiceImpl {
    /// 智能确定下一个模块类型
    ///
    /// 根据当前会话状态和自适应逻辑确定下一个应该开始的模块
    async fn determine_next_module_type(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<ModuleType> {
        info!("智能确定下一模块类型: 会话ID={}", session_id);

        // 1. 获取会话信息
        let session = self.repositories.session
            .find_by_session_id(session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        // 2. 验证用户权限
        if session.user_id != user_id {
            return Err(crate::error::Error::service("用户无权访问此会话"));
        }

        // 3. 获取当前学科的模块进度
        let progresses = self.repositories.progress
            .find_by_session_and_subject(session_id, session.current_subject)
            .await?;

        // 4. 分析当前状态，确定下一个模块
        let mut module1_completed = false;
        let mut module2_exists = None;

        for progress in &progresses {
            match progress.module_type {
                ModuleType::Module1 => {
                    if progress.module_status.is_completed() {
                        module1_completed = true;
                    }
                }
                ModuleType::Module2E => {
                    module2_exists = Some(ModuleType::Module2E);
                }
                ModuleType::Module2H => {
                    module2_exists = Some(ModuleType::Module2H);
                }
            }
        }

        // 5. 根据状态确定下一个模块
        if !module1_completed {
            // 模块1未完成，下一个应该是模块1
            info!("模块1未完成，返回模块1");
            Ok(ModuleType::Module1)
        } else if let Some(module2_type) = module2_exists {
            // 模块1已完成，模块2已通过自适应逻辑确定
            info!("模块1已完成，自适应确定的模块2类型: {:?}", module2_type);
            Ok(module2_type)
        } else {
            // 模块1已完成但模块2未创建，触发自适应逻辑
            warn!("模块1已完成但模块2未创建，触发自适应逻辑");

            // 使用自适应服务创建并确定模块2类型
            let adaptive_service = crate::domain::exam::services::AdaptiveModuleService::new(
                self.repositories.answer.clone(),
                self.repositories.progress.clone(),
            );

            let module2_type = adaptive_service
                .evaluate_and_create_module2(session_id, session.current_subject)
                .await?;

            info!("自适应逻辑确定模块2类型: {:?}", module2_type);
            Ok(module2_type)
        }
    }
}

// 辅助方法实现
impl EnhancedExamServiceImpl {
    /// 构建模块详细信息
    async fn build_module_details(&self, session_id: &str) -> Result<Vec<ModuleDetailDto>> {
        let mut details = Vec::new();

        // 获取所有模块进度
        let progresses = self.repositories.progress
            .find_by_session_id(session_id)
            .await?;

        for progress in progresses {
            let answers = self.repositories.answer
                .find_by_session_and_module(session_id, progress.module_type)
                .await?;

            let detail = ModuleDetailDto {
                module_type: progress.module_type,
                module_status: progress.module_status,
                subject: progress.subject,
                subject_name: self.get_subject_name(progress.subject),
                progress: crate::application::exam::dto::ModuleProgressDto {
                    module_type: progress.module_type,
                    subject: progress.subject,
                    subject_name: self.get_subject_name(progress.subject),
                    total_questions: progress.total_questions,
                    answered_questions: answers.len() as i32,
                    correct_questions: answers.iter().filter(|a| a.is_correct.unwrap_or(false)).count() as i32,
                    time_limit_seconds: progress.time_limit_seconds,
                    time_used_seconds: progress.time_used_seconds,
                    remaining_time_seconds: progress.remaining_time_seconds,
                    module_status: progress.module_status,
                    started_at: progress.started_at,
                    completed_at: progress.completed_at,
                },
                score: None, // TODO: 如果已提交，获取成绩信息
                statistics: crate::application::exam::dto::ModuleStatisticsDto {
                    total_questions: progress.total_questions,
                    answered_questions: answers.len() as i32,
                    correct_questions: answers.iter().filter(|a| a.is_correct.unwrap_or(false)).count() as i32,
                    skipped_questions: 0, // TODO: 计算跳过的题目
                    average_time_per_question: if !answers.is_empty() {
                        answers.iter()
                            .filter_map(|a| a.response_time_seconds)
                            .sum::<i32>() as f64 / answers.len() as f64
                    } else {
                        0.0
                    },
                    total_time_seconds: progress.time_used_seconds,
                    remaining_time_seconds: progress.remaining_time_seconds.unwrap_or(0),
                    completion_rate: answers.len() as f64 / progress.total_questions as f64,
                    accuracy_rate: if !answers.is_empty() {
                        answers.iter().filter(|a| a.is_correct.unwrap_or(false)).count() as f64 / answers.len() as f64
                    } else {
                        0.0
                    },
                },
                is_current: progress.module_status == ModuleStatus::InProgress,
                can_start: progress.module_status == ModuleStatus::NotStarted,
                can_submit: progress.module_status == ModuleStatus::InProgress && !answers.is_empty(),
            };

            details.push(detail);
        }

        Ok(details)
    }

    /// 构建答题历史
    async fn build_answer_history(
        &self,
        session_id: &str,
        limit: Option<i32>
    ) -> Result<Vec<AnswerHistoryDto>> {
        let answers = self.repositories.answer
            .find_by_session_id(session_id)
            .await?;

        let mut history: Vec<AnswerHistoryDto> = answers
            .into_iter()
            .take(limit.unwrap_or(100) as usize)
            .map(|answer| AnswerHistoryDto {
                question_id: answer.question_id,
                module_type: answer.module_type,
                question_sequence: answer.module_sequence,
                student_answer: answer.user_answer,
                is_correct: answer.is_correct,
                time_spent_seconds: answer.response_time_seconds,
                answered_at: Some(answer.created_at),
                question_summary: None, // TODO: 获取题目摘要
            })
            .collect();

        // 按答题时间倒序排列
        history.sort_by(|a, b| b.answered_at.cmp(&a.answered_at));

        Ok(history)
    }

    /// 构建时间信息
    async fn build_time_info(&self, session_info: &SessionInfoDto) -> Result<TimeInfoDto> {
        let current_time = Utc::now();
        let elapsed_seconds = (current_time - session_info.created_at).num_seconds() as i32;
        let total_time_seconds = session_info.total_time_minutes * 60;
        let remaining_seconds = (total_time_seconds - elapsed_seconds).max(0);

        Ok(TimeInfoDto {
            exam_start_time: session_info.created_at,
            current_time,
            elapsed_seconds,
            remaining_seconds,
            is_near_timeout: remaining_seconds < 600, // 少于10分钟
            timeout_warning_threshold: 600,
        })
    }

    /// 生成推荐操作
    async fn generate_recommendations(
        &self,
        exam_state: &crate::application::exam::dto::ExamStateDto,
        time_info: &TimeInfoDto,
    ) -> Result<Vec<RecommendationDto>> {
        let mut recommendations = Vec::new();

        // 时间管理推荐
        if time_info.is_near_timeout {
            recommendations.push(RecommendationDto {
                recommendation_type: RecommendationType::TimeManagement,
                title: "时间不足警告".to_string(),
                description: format!("剩余时间不足{}分钟，请合理分配时间", time_info.remaining_seconds / 60),
                priority: 5,
                parameters: Some(serde_json::json!({"remaining_seconds": time_info.remaining_seconds})),
            });
        }

        // 答题策略推荐
        if let Some(current_module) = &exam_state.current_module {
            let completion_rate = current_module.answered_questions as f64 / current_module.total_questions as f64;
            if completion_rate < 0.5 {
                recommendations.push(RecommendationDto {
                    recommendation_type: RecommendationType::AnsweringStrategy,
                    title: "答题进度提醒".to_string(),
                    description: "当前模块完成度较低，建议加快答题速度".to_string(),
                    priority: 3,
                    parameters: Some(serde_json::json!({"completion_rate": completion_rate})),
                });
            }
        }

        Ok(recommendations)
    }

    /// 获取学科名称
    fn get_subject_name(&self, subject: crate::domain::exam::Subject) -> String {
        match subject {
            crate::domain::exam::Subject::Reading => "阅读".to_string(),
            crate::domain::exam::Subject::Math => "数学".to_string(),
        }
    }
}
