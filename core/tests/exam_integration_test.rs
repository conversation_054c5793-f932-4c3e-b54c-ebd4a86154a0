//! SAT考试系统集成测试
//!
//! 测试考试系统的核心功能

use recommendation_core::application::exam::{
    CreateExamSessionRequestDto, StartModuleRequestDto, SubmitAnswerRequestDto,
};
use recommendation_core::domain::exam::{
    ExamType, ModuleType, Subject,
};

#[tokio::test]
async fn test_exam_flow_basic() {
    println!("🧪 开始基础考试流程测试");
    
    // 这是一个基础的结构测试，验证我们的类型定义是否正确
    
    // 1. 测试创建会话请求
    let create_request = CreateExamSessionRequestDto {
        user_id: 12345,
        paper_id: 700540150387707904,
        exam_type: ExamType::Full,
    };
    
    println!("✅ 创建会话请求构建成功: user_id={}, paper_id={}", 
             create_request.user_id, create_request.paper_id);
    
    // 2. 测试开始模块请求
    let start_module_request = StartModuleRequestDto {
        session_id: "test_session_123".to_string(),
        user_id: 12345,
        module_type: ModuleType::Module1,
    };
    
    println!("✅ 开始模块请求构建成功: session_id={}, module_type={:?}", 
             start_module_request.session_id, start_module_request.module_type);
    
    // 3. 测试提交答案请求
    let submit_answer_request = SubmitAnswerRequestDto {
        session_id: "test_session_123".to_string(),
        user_id: 12345,
        question_id: 1001,
        student_answer: "A".to_string(),
        time_spent_seconds: 120,
        module_type: ModuleType::Module1,
        question_sequence: 1,
    };
    
    println!("✅ 提交答案请求构建成功: question_id={}, answer={}", 
             submit_answer_request.question_id, submit_answer_request.student_answer);
    
    // 4. 测试模块类型功能
    let math_questions = ModuleType::Module1.question_count(Subject::Math);
    let reading_questions = ModuleType::Module1.question_count(Subject::Reading);
    let math_time = ModuleType::Module1.time_limit_minutes(Subject::Math);
    let reading_time = ModuleType::Module1.time_limit_minutes(Subject::Reading);
    
    println!("✅ 模块配置测试:");
    println!("   数学模块: {}题, {}分钟", math_questions, math_time);
    println!("   语言模块: {}题, {}分钟", reading_questions, reading_time);
    
    // 5. 测试考试类型
    println!("✅ 考试类型测试: {:?}", ExamType::Full);
    
    println!("🎉 基础考试流程测试完成！");
}

#[tokio::test]
async fn test_module_types() {
    println!("🧪 开始模块类型测试");
    
    let modules = vec![
        ModuleType::Module1,
        ModuleType::Module2E,
        ModuleType::Module2H,
    ];
    
    for module in modules {
        println!("📋 测试模块: {:?}", module);
        
        // 测试是否为第二模块
        let is_second = module.is_second_module();
        println!("   是否为第二模块: {}", is_second);
        
        // 测试题目数量
        let math_count = module.question_count(Subject::Math);
        let reading_count = module.question_count(Subject::Reading);
        println!("   数学题目数: {}, 语言题目数: {}", math_count, reading_count);
        
        // 测试时间限制
        let math_time = module.time_limit_minutes(Subject::Math);
        let reading_time = module.time_limit_minutes(Subject::Reading);
        println!("   数学时间: {}分钟, 语言时间: {}分钟", math_time, reading_time);
    }
    
    println!("🎉 模块类型测试完成！");
}

#[test]
fn test_value_objects() {
    println!("🧪 开始值对象测试");
    
    // 测试学科
    let subjects = vec![Subject::Math, Subject::Reading];
    for subject in subjects {
        println!("📚 学科: {:?}", subject);
    }
    
    // 测试考试类型
    let exam_types = vec![ExamType::Full];
    for exam_type in exam_types {
        println!("📝 考试类型: {:?}", exam_type);
    }
    
    println!("🎉 值对象测试完成！");
}

#[test]
fn test_dto_serialization() {
    println!("🧪 开始DTO序列化测试");
    
    // 测试创建会话请求的序列化
    let create_request = CreateExamSessionRequestDto {
        user_id: 12345,
        paper_id: 700540150387707904,
        exam_type: ExamType::Full,
    };
    
    // 这里我们只是验证结构体可以正常创建
    println!("✅ CreateExamSessionRequestDto 创建成功");
    println!("   user_id: {}", create_request.user_id);
    println!("   paper_id: {}", create_request.paper_id);
    println!("   exam_type: {:?}", create_request.exam_type);
    
    println!("🎉 DTO序列化测试完成！");
}

/// 测试错误处理
#[test]
fn test_error_handling() {
    println!("🧪 开始错误处理测试");
    
    // 测试无效的模块类型字符串解析
    let invalid_module = ModuleType::from_str("invalid");
    assert!(invalid_module.is_none(), "无效模块类型应该返回None");
    
    // 测试有效的模块类型字符串解析
    let valid_modules = vec![
        ("1", Some(ModuleType::Module1)),
        ("2E", Some(ModuleType::Module2E)),
        ("2H", Some(ModuleType::Module2H)),
    ];
    
    for (input, expected) in valid_modules {
        let result = ModuleType::from_str(input);
        assert_eq!(result, expected, "模块类型解析失败: {}", input);
        println!("✅ 模块类型解析成功: {} -> {:?}", input, result);
    }
    
    println!("🎉 错误处理测试完成！");
}
